-- RLS Policy Verification and Fix Script for quote_mappings table
-- Run this in your TripXplo Quote Supabase SQL Editor

-- 1. Check current RLS status
SELECT 
    schemaname,
    tablename,
    rowsecurity,
    CASE 
        WHEN rowsecurity THEN 'RLS is ENABLED'
        ELSE 'RLS is DISABLED'
    END as rls_status
FROM pg_tables 
WHERE tablename = 'quote_mappings' 
AND schemaname = 'public';

-- 2. Check existing policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'quote_mappings'
ORDER BY cmd;

-- 3. Check if policies exist for all operations
SELECT 
    'SELECT' as operation,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_policies 
            WHERE tablename = 'quote_mappings' 
            AND cmd = 'SELECT'
        ) THEN 'Policy EXISTS'
        ELSE 'Policy MISSING'
    END as status
UNION ALL
SELECT 
    'INSERT' as operation,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_policies 
            WHERE tablename = 'quote_mappings' 
            AND cmd = 'INSERT'
        ) THEN 'Policy EXISTS'
        ELSE 'Policy MISSING'
    END as status
UNION ALL
SELECT 
    'UPDATE' as operation,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_policies 
            WHERE tablename = 'quote_mappings' 
            AND cmd = 'UPDATE'
        ) THEN 'Policy EXISTS'
        ELSE 'Policy MISSING'
    END as status
UNION ALL
SELECT 
    'DELETE' as operation,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_policies 
            WHERE tablename = 'quote_mappings' 
            AND cmd = 'DELETE'
        ) THEN 'Policy EXISTS'
        ELSE 'Policy MISSING'
    END as status;

-- 4. If policies are missing, create them
-- (Uncomment the following lines if policies are missing)

/*
-- Enable RLS if not already enabled
ALTER TABLE quote_mappings ENABLE ROW LEVEL SECURITY;

-- Create missing policies (these allow all authenticated users)
CREATE POLICY "Users can view all quote mappings" ON quote_mappings
    FOR SELECT USING (true);

CREATE POLICY "Users can insert quote mappings" ON quote_mappings
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update quote mappings" ON quote_mappings
    FOR UPDATE USING (true);

CREATE POLICY "Users can delete quote mappings" ON quote_mappings
    FOR DELETE USING (true);
*/

-- 5. Test basic access (this should work if policies are correct)
-- This will show if you can access the table
SELECT 
    COUNT(*) as total_mappings,
    'Access test SUCCESSFUL' as result
FROM quote_mappings;

-- 6. Check table permissions
SELECT 
    grantee,
    privilege_type,
    is_grantable
FROM information_schema.table_privileges 
WHERE table_name = 'quote_mappings' 
AND table_schema = 'public'
ORDER BY grantee, privilege_type;

-- 7. Final verification
SELECT 
    'quote_mappings table verification complete' as message,
    CASE 
        WHEN EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'quote_mappings')
        THEN 'Table EXISTS'
        ELSE 'Table MISSING'
    END as table_status,
    CASE 
        WHEN (SELECT rowsecurity FROM pg_tables WHERE tablename = 'quote_mappings' AND schemaname = 'public')
        THEN 'RLS ENABLED'
        ELSE 'RLS DISABLED'
    END as rls_status,
    (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'quote_mappings') as policy_count;
