import { supabase } from './supabaseClient';
import { executeWithRetryAndTimeout } from './apiUtils';

export async function signUpWithEmail(email: string, password: string, fullName: string) {
  try {
    console.log('[authService] Attempting sign up with email...');

    // Use retry and timeout logic for sign up
    const { data, error } = await executeWithRetryAndTimeout(
      () => supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      }),
      {
        maxRetries: 2, // Max 2 retries (3 attempts total)
        initialDelay: 300, // Start with 300ms delay
        timeoutMs: 20000 // 20 second timeout for signup
      }
    );

    if (error) {
      console.error('[authService] Error signing up:', error.message);
      throw error;
    }

    console.log('[authService] Sign up successful');
    // The trigger on auth.users will handle profile creation
    // No need to manually insert into the profiles table

    return data;
  } catch (error) {
    console.error('[authService] Unexpected error during sign up:', error);
    throw error;
  }
}

export async function signInWithPassword(email: string, password: string) {
  try {
    console.log('[authService] Attempting sign in with password...');

    // Use retry and timeout logic for sign in
    const { data, error } = await executeWithRetryAndTimeout(
      () => supabase.auth.signInWithPassword({ email, password }),
      {
        maxRetries: 2, // Max 2 retries (3 attempts total)
        initialDelay: 300, // Start with 300ms delay
        timeoutMs: 20000 // 20 second timeout for login
      }
    );

    if (error) {
      console.error('[authService] Error in signInWithPassword:', error);
      throw error;
    }

    console.log('[authService] Sign in successful');
    return data;
  } catch (error) {
    console.error('[authService] Error signing in:', error);
    throw error;
  }
}

export async function signOutUser() {
  try {
    console.log('[authService] Attempting sign out...');

    // Use retry and timeout logic for sign out
    const { error } = await executeWithRetryAndTimeout(
      () => supabase.auth.signOut(),
      {
        maxRetries: 1, // Max 1 retry (2 attempts total)
        initialDelay: 300, // Start with 300ms delay
        timeoutMs: 10000, // 10 second timeout for signout
        fallbackFn: async () => {
          console.log('[authService] Sign out timed out, forcing sign out in localStorage');
          // Force clear localStorage as a fallback
          try {
            localStorage.removeItem('tripxplo-supabase-auth');
          } catch (e) {
            console.error('[authService] Error clearing localStorage:', e);
          }
          return { error: null };
        }
      }
    );

    if (error) {
      console.error('[authService] Error in signOutUser:', error);
      throw error;
    }

    console.log('[authService] Sign out successful');
  } catch (error) {
    console.error('[authService] Error signing out:', error);
    throw error;
  }
}



/**
 * Get session data from localStorage as a fallback
 * @returns Partial session object with user data, or null if not found
 */
function getSessionFromLocalStorage() {
  try {
    console.log('[AuthContext] Attempting fallback session retrieval from storage...');
    // Access localStorage directly as a fallback mechanism
    const storageKey = 'tripxplo-supabase-auth'; // Must match the key in supabaseClient.ts
    const sessionStr = localStorage.getItem(storageKey);

    if (sessionStr) {
      try {
        const sessionData = JSON.parse(sessionStr);
        console.log('[AuthContext] Found session data in localStorage, checking validity...');

        // Check if we have tokens and user data
        if (sessionData?.access_token && sessionData?.refresh_token) {
          console.log('[AuthContext] Found valid tokens in localStorage session');

          // Check if we have user data
          if (sessionData.user) {
            console.log('[AuthContext] Extracted user from localStorage session');
            return { user: sessionData.user };
          }
        }
      } catch (parseError) {
        console.error('[AuthContext] Error parsing session from localStorage:', parseError);
      }
    } else {
      console.log('[AuthContext] No session data found in localStorage');
    }
  } catch (storageError) {
    console.error('[AuthContext] Error accessing localStorage:', storageError);
  }

  return null;
}

export async function getCurrentSession() {
  try {
    console.log('[AuthContext] Attempting to get current session...');

    // First check localStorage immediately
    const localSession = getSessionFromLocalStorage();
    if (localSession) {
      console.log('[AuthContext] Using session from localStorage');

      // Start a background refresh of the session
      setTimeout(() => {
        console.log('[AuthContext] Attempting background session refresh...');
        supabase.auth.refreshSession()
          .then(({ data }) => {
            if (data.session) {
              console.log('[AuthContext] Background session refresh successful');
            }
          })
          .catch(err => console.error('[AuthContext] Background session refresh error:', err));
      }, 100);

      return localSession;
    }

    // If no localStorage session, try API directly
    console.log('[AuthContext] No localStorage session, checking API...');
    const { data, error } = await supabase.auth.getSession();

    console.log('[AuthContext] API session check completed. Session/User:', data);
    if (error) throw error;
    return data.session;
  } catch (error) {
    console.error('[AuthContext] Error during session check:', error);

    // Final fallback - check localStorage again
    const localSession = getSessionFromLocalStorage();
    if (localSession) {
      return localSession;
    }

    return null; // Return null instead of throwing to avoid crashes
  }
}

export async function getUserProfile(userId: string) {
  try {
    console.log('[authService] Fetching user profile for ID:', userId);

    // Use retry and timeout logic for profile fetch with shorter timeout
    const { data, error } = await executeWithRetryAndTimeout(
      async () => await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single(),
      {
        maxRetries: 1, // Reduced to 1 retry (2 attempts total)
        initialDelay: 200, // Reduced initial delay
        timeoutMs: 3000 // Reduced to 3 second timeout for faster response
      }
    );

    if (error) {
      // Check if this is a timeout or network error vs. a data error
      if (error.message?.includes('timeout') || error.message?.includes('network') || error.code === 'ECONNABORTED') {
        console.warn('[authService] Profile fetch timed out or network error:', error);
        // Return null but don't throw - this allows the UI to continue
        return null;
      }

      console.error('[authService] Error in getUserProfile:', error);
      throw error;
    }

    console.log('[authService] User profile fetch successful');
    return data;
  } catch (error) {
    console.error('[authService] Error fetching user profile:', error);
    return null;
  }
}