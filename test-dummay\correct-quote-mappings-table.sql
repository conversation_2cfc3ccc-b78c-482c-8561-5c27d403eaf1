-- CORRECTED quote_mappings table structure
-- Run this in your Quote Supabase SQL Editor

-- Drop existing table if you want to recreate it (CAREFUL - this will delete data!)
-- DROP TABLE IF EXISTS public.quote_mappings CASCADE;

-- Create the corrected table
CREATE TABLE IF NOT EXISTS public.quote_mappings (
    id UUID NOT NULL DEFAULT gen_random_uuid(),
    quote_id UUID NOT NULL,  -- Changed from NULL to NOT NULL (this should be required)
    quote_name TEXT NOT NULL,
    customer_name TEXT NOT NULL,
    destination TEXT NOT NULL,
    hotel_mappings JSONB NOT NULL DEFAULT '[]'::jsonb,  -- Changed to NOT NULL
    vehicle_mappings JSONB NOT NULL DEFAULT '[]'::jsonb,  -- Changed to NOT NULL
    additional_costs JSONB NOT NULL DEFAULT '{
        "meal_cost_per_person": 0,
        "ferry_cost": 0,
        "activity_cost_per_person": 0,
        "guide_cost_per_day": 0,
        "parking_toll_multiplier": 1.0
    }'::jsonb,  -- Changed to NOT NULL and corrected structure
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),  -- Changed to NOT NULL and NOW()
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),  -- Changed to NOT NULL and NOW()
    
    -- Constraints
    CONSTRAINT quote_mappings_pkey PRIMARY KEY (id),
    CONSTRAINT quote_mappings_quote_id_key UNIQUE (quote_id),  -- Added explicit unique constraint
    CONSTRAINT quote_mappings_quote_id_fkey FOREIGN KEY (quote_id) 
        REFERENCES quotes(id) ON DELETE CASCADE
);

-- Create indexes
CREATE UNIQUE INDEX IF NOT EXISTS idx_quote_mappings_quote_id 
    ON public.quote_mappings USING btree (quote_id);

CREATE INDEX IF NOT EXISTS idx_quote_mappings_created_at 
    ON public.quote_mappings USING btree (created_at);

CREATE INDEX IF NOT EXISTS idx_quote_mappings_destination 
    ON public.quote_mappings USING btree (destination);

-- Enable RLS (Row Level Security)
ALTER TABLE public.quote_mappings ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view all quote mappings" ON public.quote_mappings;
DROP POLICY IF EXISTS "Users can insert quote mappings" ON public.quote_mappings;
DROP POLICY IF EXISTS "Users can update quote mappings" ON public.quote_mappings;
DROP POLICY IF EXISTS "Users can delete quote mappings" ON public.quote_mappings;

-- Create RLS policies
CREATE POLICY "Users can view all quote mappings" ON public.quote_mappings
    FOR SELECT USING (true);

CREATE POLICY "Users can insert quote mappings" ON public.quote_mappings
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update quote mappings" ON public.quote_mappings
    FOR UPDATE USING (true);

CREATE POLICY "Users can delete quote mappings" ON public.quote_mappings
    FOR DELETE USING (true);

-- Grant permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.quote_mappings TO anon, authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Add comments
COMMENT ON TABLE public.quote_mappings IS 'Enhanced quote data for family type pricing calculations';
COMMENT ON COLUMN public.quote_mappings.hotel_mappings IS 'Array of hotel cost mappings with extra adult, children, infant costs';
COMMENT ON COLUMN public.quote_mappings.vehicle_mappings IS 'Array of vehicle cost mappings with pricing models and multipliers';
COMMENT ON COLUMN public.quote_mappings.additional_costs IS 'Additional cost mappings for meals, ferry, activities, guide, parking/toll';

-- Verify the table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'quote_mappings' 
AND table_schema = 'public'
ORDER BY ordinal_position;
