import React, { useState, useEffect, useRef } from 'react';

interface HotelAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const hotelList = [
  "German Residency, Srinagar",
  "Woodstock Resorts, Coorg",
    "German Residency, Srinagar",
    "Woodstock Resorts, Coorg",
    "Villa Park, Mysore",
    "Rosepark Residency, Ooty",
    "Chalston Beach Resort, Goa",
    "Riverside Regency Resort, Goa",
    "Hotel Candolim Grande, Goa",
    "Casa Seaesta Beach Cottages, Goa",
    "Vedatmana Boa Ventura, Goa",
    "Grand Vatika Resort, Goa",
    "Kalki Resort, Goa",
    "Ginger Tree Goveia Suites - Candolim, Goa",
    "Hotel Helia, Gangtok",
    "Gonday Village Resort, Sikkim",
    "Mandarin Village Resort, Sikkim",
    "Hotel Flora Fountain, Gangtok",
    "Suhim Portico Heritage, Sikkim",
    "Noble Heritage Hotel & Resort, Gangtok",
    "Rufina Hotels, Kolkata",
    "Hotel Sonar Tori, Gangtok",
    "Hotel Rabdentsie Residency, Sikkim",
    "The Planters Home, Sikkim",
    "Hotel Snow View On Cloud Nine, Sikkim",
    "Hotel Le Coorg, Coorg",
    "Parampara Resort & Spa, Coorg"
];

export const HotelAutocomplete: React.FC<HotelAutocompleteProps> = ({
  value,
  onChange,
  placeholder = "Search hotels...",
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const wrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const filteredHotels = hotelList.filter(hotel =>
    hotel.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelect = (hotel: string) => {
    onChange(hotel);
    setSearchTerm("");
    setIsOpen(false);
  };

  return (
    <div ref={wrapperRef} className="relative w-full">
      <input
        type="text"
        value={value}
        onChange={(e) => {
          onChange(e.target.value);
          setSearchTerm(e.target.value);
          setIsOpen(true);
        }}
        onFocus={() => setIsOpen(true)}
        placeholder={placeholder}
        className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 
                   focus:ring-blue-500 focus:border-blue-500 ${className}`}
      />

      {isOpen && filteredHotels.length > 0 && (
        <div className="absolute left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-xl z-[9999] fixed">
          <div className="max-h-[300px] overflow-y-auto">
            {filteredHotels.map((hotel, index) => (
              <div
                key={index}
                className="px-4 py-2.5 hover:bg-blue-50 cursor-pointer text-sm border-b border-gray-100 last:border-0"
                onClick={() => handleSelect(hotel)}
              >
                {hotel}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
