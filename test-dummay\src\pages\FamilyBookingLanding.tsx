import React, { useState, useEffect, useRef } from 'react';
import { MapPin, Calendar, Users, Sparkles, ChevronDown, X, Plus, Minus, Package, CreditCard, Eye } from 'lucide-react';
import { familyBookingService, type Destination, type FamilyType, type PackageResult, type SearchResponse } from '../lib/familyBookingService';

interface TravelerCounts {
  adults: number;
  children: number;
  infants: number;
}

// Package Card Component
const PackageCard: React.FC<{ package: PackageResult }> = ({ package: pkg }) => {
  const formatCurrency = (amount: number) => `₹${amount.toLocaleString()}`;

  const bestEMI = pkg.emi_options.find(emi => emi.is_featured) || pkg.emi_options[0];

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
      {/* Package Image */}
      <div className="relative h-48 bg-gradient-to-r from-blue-500 to-purple-600">
        <div className="absolute inset-0 bg-black bg-opacity-20"></div>
        <div className="absolute top-4 left-4">
          <span className="bg-white bg-opacity-90 text-gray-800 px-3 py-1 rounded-full text-sm font-medium">
            {pkg.duration_days}N / {pkg.duration_days + 1}D
          </span>
        </div>
        <div className="absolute top-4 right-4">
          <span className="bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1">
            <Sparkles className="w-3 h-3" />
            Featured
          </span>
        </div>
        <div className="absolute bottom-4 left-4 text-white">
          <h3 className="text-xl font-bold">{pkg.title}</h3>
          <p className="text-sm opacity-90">{pkg.destination}</p>
        </div>
      </div>

      {/* Package Content */}
      <div className="p-6">
        {/* Inclusions */}
        <div className="flex flex-wrap gap-2 mb-4">
          {pkg.inclusions.slice(0, 4).map((inclusion, index) => (
            <span key={index} className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
              {inclusion}
            </span>
          ))}
        </div>

        {/* Pricing */}
        <div className="mb-4">
          <div className="text-2xl font-bold text-gray-900">{formatCurrency(pkg.total_price)}</div>
          <div className="text-sm text-gray-500">Total package price</div>
        </div>

        {/* EMI Options */}
        {bestEMI && (
          <div className="bg-blue-50 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-blue-800">EMI Option</span>
              <CreditCard className="w-4 h-4 text-blue-600" />
            </div>
            <div className="text-lg font-bold text-blue-900">
              {formatCurrency(bestEMI.monthly_amount)}/month
            </div>
            <div className="text-sm text-blue-700">
              for {bestEMI.months} months • Total: {formatCurrency(bestEMI.total_amount)}
            </div>
          </div>
        )}

        {/* Action Button */}
        <button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center gap-2">
          <Eye className="w-4 h-4" />
          View Details & Book
        </button>
      </div>
    </div>
  );
};

const FamilyBookingLanding: React.FC = () => {
  const [destinations, setDestinations] = useState<Destination[]>([]);
  const [familyTypes, setFamilyTypes] = useState<FamilyType[]>([]);
  const [filteredDestinations, setFilteredDestinations] = useState<Destination[]>([]);
  const [showDestinationSuggestions, setShowDestinationSuggestions] = useState(false);
  const [showTravelerModal, setShowTravelerModal] = useState(false);
  const [currentHighlight, setCurrentHighlight] = useState(-1);
  const [searchResults, setSearchResults] = useState<SearchResponse | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);

  // Form state
  const [destination, setDestination] = useState('');
  const [travelDate, setTravelDate] = useState('');
  const [travelers, setTravelers] = useState<TravelerCounts>({
    adults: 2,
    children: 0,
    infants: 0
  });
  
  const destinationInputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      // Load destinations using the service
      const destinationsResponse = await familyBookingService.getDestinations();
      if (destinationsResponse.success) {
        setDestinations(destinationsResponse.data);
      } else {
        console.error('Error loading destinations:', destinationsResponse.error);
      }

      // Load family types using the service
      const familyTypesResponse = await familyBookingService.getFamilyTypes();
      if (familyTypesResponse.success) {
        setFamilyTypes(familyTypesResponse.data);
      } else {
        console.error('Error loading family types:', familyTypesResponse.error);
      }
    } catch (error) {
      console.error('Error loading initial data:', error);
    }
  };

  // Handle destination input
  const handleDestinationInput = (value: string) => {
    setDestination(value);
    
    if (value.length < 2) {
      setShowDestinationSuggestions(false);
      return;
    }

    const filtered = destinations.filter(dest =>
      dest.destination.toLowerCase().includes(value.toLowerCase()) ||
      dest.category.toLowerCase().includes(value.toLowerCase())
    ).slice(0, 8);

    setFilteredDestinations(filtered);
    setShowDestinationSuggestions(filtered.length > 0);
    setCurrentHighlight(-1);
  };

  // Handle keyboard navigation for destination suggestions
  const handleDestinationKeyDown = (e: React.KeyboardEvent) => {
    if (!showDestinationSuggestions) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setCurrentHighlight(prev => 
          Math.min(prev + 1, filteredDestinations.length - 1)
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setCurrentHighlight(prev => Math.max(prev - 1, -1));
        break;
      case 'Enter':
        e.preventDefault();
        if (currentHighlight >= 0 && filteredDestinations[currentHighlight]) {
          selectDestination(filteredDestinations[currentHighlight].destination);
        }
        break;
      case 'Escape':
        setShowDestinationSuggestions(false);
        break;
    }
  };

  const selectDestination = (destinationName: string) => {
    setDestination(destinationName);
    setShowDestinationSuggestions(false);
    setCurrentHighlight(-1);
  };

  // Detect family type based on traveler counts
  const detectFamilyType = (): FamilyType | null => {
    return familyBookingService.findMatchingFamilyType(
      familyTypes,
      travelers.adults,
      travelers.children,
      travelers.infants
    );
  };

  const getTravelerDisplayText = () => {
    const { adults, children, infants } = travelers;
    let text = `${adults} Adult${adults > 1 ? 's' : ''}`;
    if (children > 0) text += `, ${children} Child${children > 1 ? 'ren' : ''}`;
    if (infants > 0) text += `, ${infants} Infant${infants > 1 ? 's' : ''}`;
    return text;
  };

  const updateTravelerCount = (type: keyof TravelerCounts, change: number) => {
    setTravelers(prev => {
      const newCount = Math.max(0, prev[type] + change);
      // Ensure at least 1 adult
      if (type === 'adults' && newCount < 1) return prev;
      
      return { ...prev, [type]: newCount };
    });
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!destination || !travelDate) {
      alert('Please fill in all fields');
      return;
    }

    setIsSearching(true);

    try {
      const searchParams = {
        destination,
        travel_date: travelDate,
        adults: travelers.adults,
        children: travelers.children,
        infants: travelers.infants
      };

      const response = await familyBookingService.searchPackages(searchParams);
      setSearchResults(response);
      setShowResults(true);

      // Scroll to results
      setTimeout(() => {
        const resultsSection = document.getElementById('results-section');
        if (resultsSection) {
          resultsSection.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);

    } catch (error) {
      console.error('Search error:', error);
      alert('Failed to search packages. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  const detectedFamilyType = detectFamilyType();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <span className="text-2xl font-bold">
                <span className="text-blue-600">TRIP</span>
                <span className="text-purple-600">XPLO</span>
              </span>
            </div>
            <div className="hidden md:flex space-x-8">
              <a href="#packages" className="text-gray-700 hover:text-blue-600 transition-colors">Packages</a>
              <a href="#about" className="text-gray-700 hover:text-blue-600 transition-colors">About</a>
              <a href="#contact" className="text-gray-700 hover:text-blue-600 transition-colors">Contact</a>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Your Family Adventure,<br />
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
              Planned & Paid Your Way
            </span>
          </h1>
          <p className="text-xl text-gray-600 mb-12">
            Enter your dream trip details and discover tailored EMI packages instantly
          </p>

          {/* Search Form */}
          <div className="bg-white rounded-2xl shadow-xl p-8 max-w-4xl mx-auto">
            <form onSubmit={handleSearch} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Destination Input */}
                <div className="relative">
                  <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                    <MapPin className="w-4 h-4 mr-2 text-blue-600" />
                    Where to?
                  </label>
                  <div className="relative">
                    <input
                      ref={destinationInputRef}
                      type="text"
                      value={destination}
                      onChange={(e) => handleDestinationInput(e.target.value)}
                      onKeyDown={handleDestinationKeyDown}
                      onFocus={() => destination.length >= 2 && setShowDestinationSuggestions(true)}
                      placeholder="e.g., Kashmir, Goa, Manali"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      autoComplete="off"
                    />
                    
                    {/* Destination Suggestions */}
                    {showDestinationSuggestions && (
                      <div 
                        ref={suggestionsRef}
                        className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto"
                      >
                        {filteredDestinations.map((dest, index) => (
                          <div
                            key={dest.id}
                            onClick={() => selectDestination(dest.destination)}
                            className={`px-4 py-3 cursor-pointer hover:bg-gray-50 border-b border-gray-100 last:border-b-0 ${
                              index === currentHighlight ? 'bg-blue-50' : ''
                            }`}
                          >
                            <div className="font-medium text-gray-900">{dest.destination}</div>
                            <div className="text-sm text-gray-500">{dest.category}</div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Travel Date */}
                <div>
                  <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                    <Calendar className="w-4 h-4 mr-2 text-blue-600" />
                    When are you travelling?
                  </label>
                  <input
                    type="month"
                    value={travelDate}
                    onChange={(e) => setTravelDate(e.target.value)}
                    min="2024-12"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                {/* Traveler Selector */}
                <div>
                  <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                    <Users className="w-4 h-4 mr-2 text-blue-600" />
                    Who's going?
                  </label>
                  <button
                    type="button"
                    onClick={() => setShowTravelerModal(true)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-left flex items-center justify-between hover:bg-gray-50"
                  >
                    <span>{getTravelerDisplayText()}</span>
                    <ChevronDown className="w-4 h-4 text-gray-400" />
                  </button>
                </div>
              </div>

              {/* Search Button */}
              <button
                type="submit"
                disabled={isSearching}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 px-8 rounded-lg font-semibold text-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSearching ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    Searching...
                  </>
                ) : (
                  <>
                    <Sparkles className="w-5 h-5" />
                    Find My Trip
                  </>
                )}
              </button>
            </form>

            {/* Family Type Display */}
            {detectedFamilyType && (
              <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
                <div className="text-center">
                  <span className="text-sm font-medium text-gray-600">Detected Family Type: </span>
                  <span className="text-lg font-bold text-blue-600">{detectedFamilyType.family_type}</span>
                  <div className="text-sm text-gray-500 mt-1">{detectedFamilyType.composition}</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Search Results Section */}
      {showResults && searchResults && (
        <section id="results-section" className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Results Header */}
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                {searchResults.success ? (
                  <>
                    Found {searchResults.packages.length} packages for{' '}
                    <span className="text-blue-600">
                      {searchResults.matched_family_type?.family_type || 'Your Family'}
                    </span>
                  </>
                ) : (
                  'Search Results'
                )}
              </h2>
              {searchResults.success && searchResults.packages.length > 0 && (
                <p className="text-xl text-gray-600">
                  Destination: <span className="font-semibold">{destination}</span> •
                  Travel Date: <span className="font-semibold">{new Date(travelDate + '-01').toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}</span>
                </p>
              )}
            </div>

            {/* Results Content */}
            {searchResults.success ? (
              searchResults.packages.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {searchResults.packages.map((pkg) => (
                    <PackageCard key={pkg.id} package={pkg} />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">No packages found</h3>
                  <p className="text-gray-600 mb-6">
                    {searchResults.message || 'Try searching for a different destination or travel date.'}
                  </p>
                  <button
                    onClick={() => setShowResults(false)}
                    className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Search Again
                  </button>
                </div>
              )
            ) : (
              <div className="text-center py-12">
                <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
                  <h3 className="text-lg font-semibold text-red-800 mb-2">Search Error</h3>
                  <p className="text-red-700 mb-4">{searchResults.message || 'Failed to search packages'}</p>
                  <button
                    onClick={() => setShowResults(false)}
                    className="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors"
                  >
                    Try Again
                  </button>
                </div>
              </div>
            )}
          </div>
        </section>
      )}

      {/* Traveler Selection Modal */}
      {showTravelerModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-gray-900">Select Travelers</h3>
              <button
                onClick={() => setShowTravelerModal(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-6">
              {/* Adults */}
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-gray-900">Adults</div>
                  <div className="text-sm text-gray-500">(12+ years)</div>
                </div>
                <div className="flex items-center gap-3">
                  <button
                    type="button"
                    onClick={() => updateTravelerCount('adults', -1)}
                    disabled={travelers.adults <= 1}
                    className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Minus className="w-4 h-4" />
                  </button>
                  <span className="w-8 text-center font-medium">{travelers.adults}</span>
                  <button
                    type="button"
                    onClick={() => updateTravelerCount('adults', 1)}
                    className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Children */}
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-gray-900">Children</div>
                  <div className="text-sm text-gray-500">(2-11 years)</div>
                </div>
                <div className="flex items-center gap-3">
                  <button
                    type="button"
                    onClick={() => updateTravelerCount('children', -1)}
                    disabled={travelers.children <= 0}
                    className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Minus className="w-4 h-4" />
                  </button>
                  <span className="w-8 text-center font-medium">{travelers.children}</span>
                  <button
                    type="button"
                    onClick={() => updateTravelerCount('children', 1)}
                    className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Infants */}
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-gray-900">Infants</div>
                  <div className="text-sm text-gray-500">(Under 2 years)</div>
                </div>
                <div className="flex items-center gap-3">
                  <button
                    type="button"
                    onClick={() => updateTravelerCount('infants', -1)}
                    disabled={travelers.infants <= 0}
                    className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Minus className="w-4 h-4" />
                  </button>
                  <span className="w-8 text-center font-medium">{travelers.infants}</span>
                  <button
                    type="button"
                    onClick={() => updateTravelerCount('infants', 1)}
                    className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Detected Family Type in Modal */}
              {detectedFamilyType && (
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="text-sm font-medium text-gray-600">Detected Family Type:</div>
                  <div className="text-lg font-bold text-blue-600">{detectedFamilyType.family_type}</div>
                </div>
              )}
            </div>

            {/* Apply Button */}
            <button
              onClick={() => setShowTravelerModal(false)}
              className="w-full mt-6 bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              Apply Selection
            </button>
          </div>
        </div>
      )}

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Family EMI Packages
            </h2>
            <p className="text-xl text-gray-600">
              Make your dream vacation affordable with our monthly prepaid EMI plans
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="fas fa-exchange-alt text-2xl text-blue-600"></i>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Easy Plan Swap</h3>
              <p className="text-gray-600">
                Swap plans 60 days before the travel date, change destinations or upgrade plans.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="fas fa-clock text-2xl text-green-600"></i>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No Last Minute Rush</h3>
              <p className="text-gray-600">
                Enjoy your family vacation without the stress of peak season crowds.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="fas fa-shield-alt text-2xl text-purple-600"></i>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Guaranteed Availability</h3>
              <p className="text-gray-600">
                Secure your booking without worrying about last-minute availability issues.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="fas fa-gift text-2xl text-yellow-600"></i>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Rewards on Booking</h3>
              <p className="text-gray-600">
                Earn benefits on each EMI payment and by referring friends or family.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Click outside to close suggestions */}
      {showDestinationSuggestions && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowDestinationSuggestions(false)}
        />
      )}
    </div>
  );
};

export default FamilyBookingLanding;
