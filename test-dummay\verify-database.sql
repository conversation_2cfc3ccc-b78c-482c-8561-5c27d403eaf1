-- Database Verification Script for Quote Mappings
-- Run this in your Quote Supabase SQL Editor to verify the setup

-- 1. Check if quote_mappings table exists
SELECT 
    table_name,
    table_type,
    table_schema
FROM information_schema.tables 
WHERE table_name = 'quote_mappings' 
AND table_schema = 'public';

-- 2. Check table structure if it exists
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'quote_mappings' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 3. Check indexes
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'quote_mappings';

-- 4. Check RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'quote_mappings';

-- 5. Test basic operations (if table exists)
-- This will fail gracefully if table doesn't exist
DO $$
BEGIN
    -- Try to select from the table
    IF EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'quote_mappings'
    ) THEN
        RAISE NOTICE 'quote_mappings table exists and is accessible';
        
        -- Check if we can perform basic operations
        PERFORM COUNT(*) FROM quote_mappings;
        RAISE NOTICE 'Successfully queried quote_mappings table';
        
    ELSE
        RAISE NOTICE 'quote_mappings table does NOT exist - you need to run the database-setup.sql script';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error accessing quote_mappings table: %', SQLERRM;
END $$;

-- 6. Check if quotes table exists (required for foreign key)
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_name = 'quotes' 
AND table_schema = 'public';

-- 7. Summary report
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'quote_mappings'
        ) THEN 'PASS: quote_mappings table exists'
        ELSE 'FAIL: quote_mappings table missing - run database-setup.sql'
    END as table_check,
    
    CASE 
        WHEN EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'quotes'
        ) THEN 'PASS: quotes table exists'
        ELSE 'FAIL: quotes table missing - check your database setup'
    END as quotes_table_check,
    
    CASE 
        WHEN EXISTS (
            SELECT FROM pg_policies 
            WHERE tablename = 'quote_mappings'
        ) THEN 'PASS: RLS policies exist'
        ELSE 'FAIL: RLS policies missing'
    END as rls_check;
