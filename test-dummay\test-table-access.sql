-- Test Table Access for quote_mappings
-- Run this in your Quote Supabase SQL Editor to verify access permissions

-- ============================================================================
-- 1. BASIC TABLE ACCESS TEST
-- ============================================================================
SELECT 
    'BASIC ACCESS TEST' as test_type,
    COUNT(*) as record_count,
    'SUCCESS: Can read from quote_mappings table' as result
FROM quote_mappings;

-- ============================================================================
-- 2. INSERT TEST (with immediate cleanup)
-- ============================================================================
DO $$
DECLARE
    test_id UUID := gen_random_uuid();
BEGIN
    -- Try to insert a test record
    INSERT INTO quote_mappings (
        quote_id,
        quote_name,
        customer_name,
        destination,
        hotel_mappings,
        vehicle_mappings,
        additional_costs
    ) VALUES (
        test_id,
        'ACCESS_TEST',
        'Test Customer',
        'Test Destination',
        '[]'::jsonb,
        '[]'::jsonb,
        '{}'::jsonb
    );
    
    RAISE NOTICE 'SUCCESS: Can INSERT into quote_mappings table';
    
    -- Immediately clean up the test record
    DELETE FROM quote_mappings WHERE quote_id = test_id;
    
    RAISE NOTICE 'SUCCESS: Can DELETE from quote_mappings table';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'ERROR: Cannot INSERT/DELETE - %', SQLERRM;
END $$;

-- ============================================================================
-- 3. UPDATE TEST
-- ============================================================================
DO $$
DECLARE
    test_id UUID := gen_random_uuid();
    existing_id UUID;
BEGIN
    -- First, insert a test record
    INSERT INTO quote_mappings (
        quote_id,
        quote_name,
        customer_name,
        destination
    ) VALUES (
        test_id,
        'UPDATE_TEST',
        'Test Customer',
        'Test Destination'
    );
    
    -- Try to update it
    UPDATE quote_mappings 
    SET quote_name = 'UPDATED_TEST'
    WHERE quote_id = test_id;
    
    RAISE NOTICE 'SUCCESS: Can UPDATE quote_mappings table';
    
    -- Clean up
    DELETE FROM quote_mappings WHERE quote_id = test_id;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'ERROR: Cannot UPDATE - %', SQLERRM;
        -- Try to clean up even if update failed
        DELETE FROM quote_mappings WHERE quote_id = test_id;
END $$;

-- ============================================================================
-- 4. UPSERT TEST (what the application uses)
-- ============================================================================
DO $$
DECLARE
    test_id UUID := gen_random_uuid();
BEGIN
    -- Test UPSERT operation (INSERT with ON CONFLICT)
    INSERT INTO quote_mappings (
        quote_id,
        quote_name,
        customer_name,
        destination,
        hotel_mappings,
        vehicle_mappings,
        additional_costs,
        updated_at
    ) VALUES (
        test_id,
        'UPSERT_TEST',
        'Test Customer',
        'Test Destination',
        '[]'::jsonb,
        '[]'::jsonb,
        '{}'::jsonb,
        NOW()
    )
    ON CONFLICT (quote_id) 
    DO UPDATE SET 
        quote_name = EXCLUDED.quote_name,
        updated_at = EXCLUDED.updated_at;
    
    RAISE NOTICE 'SUCCESS: Can UPSERT into quote_mappings table';
    
    -- Test the upsert update part
    INSERT INTO quote_mappings (
        quote_id,
        quote_name,
        customer_name,
        destination,
        updated_at
    ) VALUES (
        test_id,
        'UPSERT_UPDATE_TEST',
        'Test Customer Updated',
        'Test Destination',
        NOW()
    )
    ON CONFLICT (quote_id) 
    DO UPDATE SET 
        quote_name = EXCLUDED.quote_name,
        customer_name = EXCLUDED.customer_name,
        updated_at = EXCLUDED.updated_at;
    
    RAISE NOTICE 'SUCCESS: Can UPSERT UPDATE quote_mappings table';
    
    -- Clean up
    DELETE FROM quote_mappings WHERE quote_id = test_id;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'ERROR: Cannot UPSERT - %', SQLERRM;
        -- Try to clean up
        DELETE FROM quote_mappings WHERE quote_id = test_id;
END $$;

-- ============================================================================
-- 5. CHECK CURRENT USER AND ROLE
-- ============================================================================
SELECT 
    'USER INFO' as info_type,
    current_user as current_user,
    session_user as session_user,
    current_setting('role') as current_role;

-- ============================================================================
-- 6. CHECK RLS POLICIES AGAIN
-- ============================================================================
SELECT 
    'RLS POLICY CHECK' as check_type,
    policyname,
    cmd as operation,
    CASE 
        WHEN qual IS NULL OR qual = '' THEN 'No restrictions (allows all)'
        ELSE qual
    END as access_condition
FROM pg_policies 
WHERE tablename = 'quote_mappings'
ORDER BY cmd;

-- ============================================================================
-- 7. TEST SPECIFIC QUOTE_ID ACCESS
-- ============================================================================
-- This tests if you can access records with specific quote_ids
-- (useful if RLS policies are filtering by user or other conditions)

SELECT 
    'QUOTE_ID ACCESS TEST' as test_type,
    quote_id,
    quote_name,
    customer_name,
    'Can access this record' as access_status
FROM quote_mappings 
LIMIT 5;

-- ============================================================================
-- 8. FINAL SUMMARY
-- ============================================================================
SELECT 
    'ACCESS SUMMARY' as summary_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM quote_mappings LIMIT 1) 
        THEN 'SUCCESS: Full table access confirmed'
        ELSE 'INFO: Table is empty but accessible'
    END as final_result,
    
    (SELECT COUNT(*) FROM quote_mappings) as total_records,
    
    CASE 
        WHEN (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'quote_mappings') > 0
        THEN 'RLS policies are active'
        ELSE 'No RLS policies found'
    END as rls_status;
