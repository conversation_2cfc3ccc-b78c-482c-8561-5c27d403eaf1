import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { ChevronDown, ChevronUp, Setting<PERSON>, CreditCard, Users, Calculator } from 'lucide-react';

const MainNavigation: React.FC = () => {
  const location = useLocation();
  const { logout } = useAuth();
  const [isEMIDropdownOpen, setIsEMIDropdownOpen] = useState(false);

  const isActive = (path: string) => location.pathname === path;
  const isActiveSection = (paths: string[]) => paths.some(path => location.pathname.startsWith(path));

  const NavLink = ({ to, children }: { to: string; children: React.ReactNode }) => (
    <Link
      to={to}
      className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
        isActive(to)
          ? 'bg-primary-light text-primary'
          : 'text-gray-600 hover:text-primary hover:bg-primary-light/50'
      }`}
    >
      {children}
    </Link>
  );

  const DropdownNavItem = ({ 
    label, 
    isOpen, 
    onToggle, 
    children, 
    isActiveSection: isActiveDropdown 
  }: { 
    label: string; 
    isOpen: boolean; 
    onToggle: () => void; 
    children: React.ReactNode;
    isActiveSection: boolean;
  }) => (
    <div className="relative">
      <button
        onClick={onToggle}
        className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-1 ${
          isActiveDropdown
            ? 'bg-primary-light text-primary'
            : 'text-gray-600 hover:text-primary hover:bg-primary-light/50'
        }`}
      >
        {label}
        {isOpen ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
      </button>
      
      {isOpen && (
        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[200px]">
          {children}
        </div>
      )}
    </div>
  );

  const DropdownLink = ({ to, icon: Icon, children }: { 
    to: string; 
    icon?: React.ElementType;
    children: React.ReactNode 
  }) => (
    <Link
      to={to}
      className={`flex items-center gap-2 px-4 py-2 text-sm transition-colors duration-200 hover:bg-gray-50 ${
        isActive(to) ? 'bg-primary-light text-primary' : 'text-gray-600 hover:text-primary'
      }`}
      onClick={() => setIsEMIDropdownOpen(false)}
    >
      {Icon && <Icon className="w-4 h-4" />}
      {children}
    </Link>
  );

  return (
    <nav className="bg-white shadow-md border-b border-gray-200">
      <div className="container mx-auto px-4 py-3">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-1">
            <span className="text-primary font-bold text-xl mr-4">TripXplo CRM</span>
            <NavLink to="/dashboard">Dashboard</NavLink>
            <NavLink to="/leads">Leads</NavLink>
            <NavLink to="/leads/new">New Lead</NavLink>
            <NavLink to="/quotes">Quotes</NavLink>
            
            <DropdownNavItem
              label="EMI Management"
              isOpen={isEMIDropdownOpen}
              onToggle={() => setIsEMIDropdownOpen(!isEMIDropdownOpen)}
              isActiveSection={isActiveSection(['/emi', '/family-types'])}
            >
              <DropdownLink to="/emi/dashboard" icon={Settings}>
                EMI Dashboard
              </DropdownLink>
              <DropdownLink to="/emi/transactions" icon={CreditCard}>
                Payment Transactions
              </DropdownLink>
              <DropdownLink to="/family-types" icon={Users}>
                Family Type Management
              </DropdownLink>
              <DropdownLink to="/emi/calculator" icon={Calculator}>
                Prepaid EMI Calculator
              </DropdownLink>
            </DropdownNavItem>
          </div>
          
          <div className="flex items-center space-x-4">
            <button 
              onClick={() => logout()}
              className="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 rounded"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
      
      {/* Overlay to close dropdown when clicking outside */}
      {isEMIDropdownOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsEMIDropdownOpen(false)}
        />
      )}
    </nav>
  );
};

export default MainNavigation; 