import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase } from '../lib/supabaseClient';
import {
  getCurrentSession,
  getUserProfile,
  signInWithPassword,
  signUpWithEmail,
} from '../lib/authService';

export interface Profile {
  id: string;
  email: string;
  full_name: string;
  role: string;
  is_active: boolean;
  partner_id: string | null;
  profile_image_url: string | null;
}

interface AuthContextValue {
  user: User | null;
  profile: Profile | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  isLoggingOut: boolean;
  login: (email: string, password: string) => Promise<void>;
  signup: (email: string, password: string, fullName: string) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextValue | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

import { useNavigate } from 'react-router-dom';

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isLoggingOut, setIsLoggingOut] = useState<boolean>(false);
  const navigate = useNavigate();

  const isAuthenticated = !!user;

  useEffect(() => {
    console.log('AuthProvider: initializing auth');

    const initAuth = async () => {
      try {
        console.log('[AuthContext] Initializing auth...');

        // First, try to get session from localStorage immediately
        try {
          const storageKey = 'tripxplo-supabase-auth';
          const sessionStr = localStorage.getItem(storageKey);

          if (sessionStr) {
            try {
              const sessionData = JSON.parse(sessionStr);
              if (sessionData?.user) {
                console.log('[AuthContext] Found user in localStorage, setting initial state');
                // Set user immediately from localStorage to avoid showing loading state
                setUser(sessionData.user);

                // Start profile fetch in background
                getUserProfile(sessionData.user.id)
                  .then(userProfile => {
                    if (userProfile) {
                      console.log('[AuthContext] Profile loaded from background fetch');
                      setProfile(userProfile);
                    }
                  })
                  .catch(err => console.error('[AuthContext] Background profile fetch error:', err));

                // Continue with API session check in background
                getCurrentSession()
                  .then(apiSession => {
                    if (apiSession?.user) {
                      console.log('[AuthContext] API session check completed successfully');
                      // Update user if different
                      if (JSON.stringify(apiSession.user) !== JSON.stringify(sessionData.user)) {
                        console.log('[AuthContext] Updating user from API session');
                        setUser(apiSession.user);
                      }
                    }
                  })
                  .catch(err => console.error('[AuthContext] Background session check error:', err))
                  .finally(() => {
                    // Ensure loading is set to false
                    setIsLoading(false);
                  });

                // End loading state early since we have user data from localStorage
                console.log('[AuthContext] Setting isLoading to false (localStorage path)');
                setIsLoading(false);
                return; // Exit early, background tasks will continue
              }
            } catch (parseError) {
              console.error('[AuthContext] Error parsing localStorage session:', parseError);
            }
          }
        } catch (storageError) {
          console.error('[AuthContext] Error accessing localStorage:', storageError);
        }

        // If we get here, localStorage didn't have valid session data
        console.log('[AuthContext] No valid session in localStorage, checking API...');

        // Set a shorter timeout for the API session check
        const sessionPromise = getCurrentSession();
        const timeoutPromise = new Promise<null>((resolve) => {
          setTimeout(() => {
            console.log('[AuthContext] Session check timed out, continuing with null session');
            resolve(null);
          }, 5000); // Reduced timeout to 5 seconds
        });

        // Race the session check against the timeout
        const session = await Promise.race([sessionPromise, timeoutPromise]);
        console.log('[AuthContext] Initial session check completed. Result:', session);

        if (session?.user) {
          console.log('[AuthContext] User found in session, setting user state');
          setUser(session.user);

          // Start profile fetch with shorter timeout
          try {
            console.log('[AuthContext] Fetching user profile for ID:', session.user.id);
            const profilePromise = getUserProfile(session.user.id);
            const profileTimeoutPromise = new Promise<null>((resolve) => {
              setTimeout(() => {
                console.log('[AuthContext] Profile fetch timed out, continuing with null profile');
                resolve(null);
              }, 3000); // Reduced timeout to 3 seconds
            });

            const userProfile = await Promise.race([profilePromise, profileTimeoutPromise]);
            console.log('[AuthContext] Profile result:', userProfile ? 'Profile found' : 'No profile');
            setProfile(userProfile);

            // If profile fetch timed out, continue fetching in background
            if (!userProfile) {
              getUserProfile(session.user.id)
                .then(profile => {
                  if (profile) {
                    console.log('[AuthContext] Profile loaded from background fetch');
                    setProfile(profile);
                  }
                })
                .catch(err => console.error('[AuthContext] Background profile fetch error:', err));
            }
          } catch (profileError) {
            console.error('[AuthContext] Error fetching user profile:', profileError);
          }
        } else {
          console.log('[AuthContext] No active session found');
          setUser(null);
          setProfile(null);
        }
      } catch (error) {
        console.error('[AuthContext] Error initializing auth:', error);
        // Continue with null user and profile on error
        setUser(null);
        setProfile(null);
      } finally {
        console.log('[AuthContext] Setting isLoading to false (API path)');
        setIsLoading(false);
      }
    };

    initAuth();

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('[AuthContext] Auth state changed:', event, session);
      try {
        if (event === 'SIGNED_OUT' || !session || !session.user) {
          console.log('[AuthContext] SIGNED_OUT event received, clearing user state.');
          setUser(null);
          setProfile(null);
        } else {
          console.log('[AuthContext] User available in session after auth change');
          setUser(session.user);
          const userProfile = await getUserProfile(session.user.id);
          setProfile(userProfile);
        }
      } catch (error) {
        console.error('[AuthContext] Error handling auth state change:', error);
      }
    });

    return () => {
      console.log('Cleaning up auth subscriptions');
      subscription.unsubscribe();
    };
  }, []);

  const login = async (email: string, password: string): Promise<void> => {
    setIsLoading(true);
    try {
      console.log('Attempting login for:', email);
      await signInWithPassword(email, password);
      console.log('Login successful');
    } catch (error) {
      console.error('Error logging in:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signup = async (email: string, password: string, fullName: string): Promise<void> => {
    setIsLoading(true);
    try {
      console.log('Attempting signup for:', email);
      await signUpWithEmail(email, password, fullName);
      console.log('Signup successful');
    } catch (error) {
      console.error('Error signing up:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoggingOut(true);
    try {
      console.log('[AuthContext] Attempting signOut...');

      // Await signOut directly and log result
      try {
        const { error } = await supabase.auth.signOut();
        console.log('[AuthContext] signOut call finished.');
        if (error) {
          console.error('[AuthContext] Error during supabase.auth.signOut():', error);
          throw error;
        }
      } catch (signOutError) {
        console.error('[AuthContext] Error during supabase.auth.signOut():', signOutError);
        throw signOutError;
      }
      // Clear user state regardless of signOut success/failure
      setUser(null);
      setProfile(null);
    } catch (error: any) {
      console.error('[AuthContext] Error in logout function:', error);
      console.error('Full error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
        status: error.status,
        ...(error.error && { innerError: error.error })
      });

      // Force clear user state even on error
      setUser(null);
      setProfile(null);
    } finally {
      setIsLoggingOut(false);
      setIsLoading(false);

      // Use a short timeout to ensure state updates have been processed
      // before attempting navigation
      setTimeout(() => {
        console.log('[AuthContext] Navigating to login page after logout');
        navigate('/login');
      }, 100); // Short delay to ensure state updates are processed
    }
  };


  return (
    <AuthContext.Provider value={{ user, profile, isAuthenticated, isLoading, isLoggingOut, login, signup, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextValue => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};