import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { getCrmSupabaseConfig } from '../config/env';

// Singleton instance
let supabaseInstance: SupabaseClient | null = null;

/**
 * Creates and returns a Supabase client for the CRM module.
 * Uses a singleton pattern to prevent multiple instances.
 */
const createSupabaseClient = (): SupabaseClient => {
  // If we already have an instance, return it
  if (supabaseInstance) {
    return supabaseInstance;
  }

  // Load from environment configuration
  const crmConfig = getCrmSupabaseConfig();
  const supabaseUrl = crmConfig.url;
  const supabaseAnonKey = crmConfig.anonKey;

  console.log('[supabaseClient] Initializing CRM Supabase client with URL:', supabaseUrl);
  console.log('[supabaseClient] Initializing with Key:', supabaseAnonKey ? 'Exists' : 'MISSING!');

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('[supabaseClient] ERROR: CRM Supabase config missing! Please check your environment configuration.');
  }

  // Define Supabase client options with explicit auth settings and improved connection handling
  const supabaseOptions = {
    auth: {
      persistSession: true,     // Store session in local storage
      autoRefreshToken: true,   // Refresh token before expiry
      detectSessionInUrl: true, // Check URL for auth callbacks
      // Add a shorter storage key to avoid potential corruption of long keys
      storageKey: 'tripxplo-supabase-auth',
      // Use 'pkce' flow type for better security
      flowType: 'pkce' as const,  // TypeScript needs 'as const' to narrow the type
    },
    realtime: {
      // Improve connection reliability with auto-reconnect settings
      params: {
        eventsPerSecond: 10,
      },
    },
    global: {
      fetch: (input: RequestInfo | URL, init?: RequestInit) => {
        // Add request timeout
        const timeoutController = new AbortController();
        // Use a longer timeout for auth requests, shorter for other requests
        const isAuthRequest = typeof input === 'string' && input.toString().includes('/auth/v1/token');
        const timeoutDuration = isAuthRequest ? 20000 : 10000; // 20 seconds for auth, 10 seconds for other requests

        const timeoutId = setTimeout(() => {
          console.log(`[supabaseClient] Request timeout for ${typeof input === 'string' ? input.toString().split('?')[0] : 'request'} after ${timeoutDuration}ms`);
          timeoutController.abort();
        }, timeoutDuration);

        // Use the abort controller signal
        const options = {
          ...init,
          signal: timeoutController.signal,
        };

        return fetch(input, options)
          .finally(() => clearTimeout(timeoutId));
      }
    }
  };

  // Log the client options for diagnostics
  console.log('[supabaseClient] Client options:', JSON.stringify(supabaseOptions, null, 2));

  // Initialize Supabase client
  console.log('Initializing Supabase client with URL:', supabaseUrl);
  console.log('Using Supabase URL:', supabaseUrl);
  console.log('Using Supabase anon key:', supabaseAnonKey.slice(0,8) + '…');
  supabaseInstance = createClient(supabaseUrl, supabaseAnonKey, supabaseOptions);

  return supabaseInstance;
};

// Export the supabase client - lazy initialization
export const supabase = createSupabaseClient();

// Log when the module has fully loaded
console.log('[supabaseClient] Module initialization complete');

// Add connection status monitor
let connectionAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 3;

// Monitor for connection status changes
document.addEventListener('visibilitychange', () => {
  // When tab becomes visible again after being hidden
  if (document.visibilityState === 'visible') {
    console.log('[supabaseClient] Tab is now visible, checking connection...');
    // Perform a lightweight check to see if connection is active
    supabase.auth.getSession().then(({ data: _sessionData, error }) => {
      if (error) {
        console.warn('[supabaseClient] Connection may be stale after visibility change:', error.message);
        // Force-refresh if needed
        if (connectionAttempts < MAX_RECONNECT_ATTEMPTS) {
          connectionAttempts++;
          console.log(`[supabaseClient] Attempting reconnection (${connectionAttempts}/${MAX_RECONNECT_ATTEMPTS})...`);
          // Only reload if user is logged in (has auth data in localStorage)
          if (localStorage.getItem('tripxplo-supabase-auth')) {
            window.location.reload();
          }
        }
      } else {
        console.log('[supabaseClient] Connection verified after visibility change');
        connectionAttempts = 0;
      }
    });
  }
});

// Add page reload/beforeunload handler to clean up connections properly
window.addEventListener('beforeunload', () => {
  console.log('[supabaseClient] Page unloading, closing connections gracefully...');
  // No need to wait for this to complete since the page is unloading
  // But it helps Supabase know this is an intentional disconnect
  supabase.removeAllChannels();
});

// Run connectivity test AFTER the module has fully initialized
// This ensures it doesn't block other operations
setTimeout(() => {
  console.log('[supabaseClient] Starting delayed connectivity test...');

  // Create a proper timeout for the fetch operation
  const testPromise = supabase.from('leads').select('id').limit(1);

  // Track timeout ID separately instead of storing it on the promise
  let timeoutId: number | null = null;

  const timeoutPromise = new Promise<never>((_, reject) => {
    timeoutId = setTimeout(() => {
      reject(new Error('Connectivity test timed out after 15 seconds'));
    }, 15000) as unknown as number; // 15 second timeout (increased from 10)
  });

  // Race the test against a timeout, but don't block module initialization
  Promise.race([testPromise, timeoutPromise])
    .then(result => {
      // Clear timeout if the test completes
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      const { data, error } = result as any;
      if (error) {
        console.error('[supabaseClient] Connectivity test error:', error.message);
        // Don't throw an error here, just log it
        console.log('[supabaseClient] Continuing despite connectivity test error');
      } else {
        console.log('[supabaseClient] Connectivity test successful:', { data });
      }
    })
    .catch(err => {
      // Also clear timeout on error
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
      console.error('[supabaseClient] Connectivity test failed:', err.message);
      console.log('[supabaseClient] Continuing despite connectivity test failure');

      // Try a simpler health check that doesn't require database access
      supabase.auth.getSession()
        .then(() => console.log('[supabaseClient] Auth service is still accessible'))
        .catch(authErr => console.error('[supabaseClient] Auth service check failed:', authErr.message));
    });
}, 1000); // Increased delay to ensure module is fully loaded