# TripXplo Family EMI - Deployment Checklist

## 🚀 Complete Deployment Guide for family.tripxplo.com

### ✅ **Phase 1: Pre-Deployment Preparation**

- [ ] **Production files ready** (`family-tripxplo-production/` folder created)
- [ ] **Linode server created** (Ubuntu 22.04 LTS recommended)
- [ ] **Server IP address noted** (e.g., *************)
- [ ] **SSH access configured** (can connect to server)
- [ ] **Domain access confirmed** (can modify DNS for tripxplo.com)

### ✅ **Phase 2: File Upload**

#### Option A: Automated Upload (Recommended)
```bash
# Make script executable
chmod +x upload-to-linode.sh

# Run upload script
./upload-to-linode.sh
```

#### Option B: Manual Upload
```bash
# Upload files via SCP
scp -r family-tripxplo-production/* root@YOUR_LINODE_IP:/var/www/html/

# Set permissions
ssh root@YOUR_LINODE_IP "
    sudo chown -R www-data:www-data /var/www/html
    sudo chmod -R 755 /var/www/html
"
```

### ✅ **Phase 3: Server Configuration**

- [ ] **Nginx installed and running**
  ```bash
  sudo apt update
  sudo apt install nginx
  sudo systemctl start nginx
  sudo systemctl enable nginx
  ```

- [ ] **Site configuration created**
  ```bash
  sudo nano /etc/nginx/sites-available/family.tripxplo.com
  ```

- [ ] **Site enabled**
  ```bash
  sudo ln -s /etc/nginx/sites-available/family.tripxplo.com /etc/nginx/sites-enabled/
  sudo nginx -t
  sudo systemctl reload nginx
  ```

- [ ] **Firewall configured**
  ```bash
  sudo ufw allow 'Nginx Full'
  sudo ufw allow ssh
  sudo ufw enable
  ```

### ✅ **Phase 4: DNS Configuration**

- [ ] **DNS records added**
  ```
  Type: A
  Name: family
  Value: YOUR_LINODE_IP
  TTL: 300
  
  Type: A
  Name: www.family
  Value: YOUR_LINODE_IP
  TTL: 300
  ```

- [ ] **DNS propagation verified**
  ```bash
  nslookup family.tripxplo.com
  dig family.tripxplo.com
  ```

### ✅ **Phase 5: SSL Certificate**

- [ ] **Certbot installed**
  ```bash
  sudo apt install certbot python3-certbot-nginx
  ```

- [ ] **SSL certificate obtained**
  ```bash
  sudo certbot --nginx -d family.tripxplo.com -d www.family.tripxplo.com
  ```

- [ ] **Auto-renewal configured**
  ```bash
  sudo crontab -e
  # Add: 0 12 * * * /usr/bin/certbot renew --quiet
  ```

### ✅ **Phase 6: Testing & Verification**

#### Basic Functionality Tests:
- [ ] **Site loads** at http://YOUR_LINODE_IP
- [ ] **Site loads** at https://family.tripxplo.com
- [ ] **HTTPS redirect works** (http → https)
- [ ] **WWW redirect works** (www.family → family)

#### Application Tests:
- [ ] **Homepage loads completely**
- [ ] **All images display correctly**
- [ ] **CSS styles applied properly**
- [ ] **JavaScript functions work**
- [ ] **Database connection successful**
- [ ] **Package search works**
- [ ] **Family type selection works**
- [ ] **EMI calculations work**
- [ ] **Package modal opens**
- [ ] **Package card generation works**
- [ ] **Download/share functionality works**

#### Mobile & Browser Tests:
- [ ] **Mobile responsive design**
- [ ] **Chrome browser compatibility**
- [ ] **Firefox browser compatibility**
- [ ] **Safari browser compatibility**
- [ ] **Edge browser compatibility**

#### Performance Tests:
- [ ] **Page load speed < 3 seconds**
- [ ] **Images optimized and loading**
- [ ] **Gzip compression enabled**
- [ ] **Static asset caching working**

### ✅ **Phase 7: Monitoring & Maintenance**

- [ ] **Log monitoring setup**
  ```bash
  sudo tail -f /var/log/nginx/access.log
  sudo tail -f /var/log/nginx/error.log
  ```

- [ ] **Uptime monitoring configured**
  - UptimeRobot, Pingdom, or similar service

- [ ] **Backup strategy implemented**
  ```bash
  # Daily backup script
  sudo cp -r /var/www/html /var/backups/html-$(date +%Y%m%d)
  ```

- [ ] **Security updates scheduled**
  ```bash
  sudo apt update && sudo apt upgrade
  ```

### ✅ **Phase 8: Go-Live Checklist**

#### Final Pre-Launch Verification:
- [ ] **All functionality tested end-to-end**
- [ ] **Database connections stable**
- [ ] **SSL certificate valid and trusted**
- [ ] **Performance benchmarks met**
- [ ] **Mobile experience optimized**
- [ ] **Error handling working properly**

#### Launch Day Tasks:
- [ ] **DNS TTL reduced** (for quick changes if needed)
- [ ] **Monitoring alerts active**
- [ ] **Team notified of go-live**
- [ ] **Rollback plan prepared**

#### Post-Launch Monitoring (First 24 Hours):
- [ ] **Monitor server resources** (CPU, memory, disk)
- [ ] **Check error logs** for any issues
- [ ] **Verify user interactions** working properly
- [ ] **Monitor database performance**
- [ ] **Check SSL certificate status**

### 🎯 **Success Criteria**

Your deployment is successful when:
- ✅ https://family.tripxplo.com loads quickly and correctly
- ✅ All package search and booking functionality works
- ✅ Mobile experience is smooth and responsive
- ✅ SSL certificate is valid and trusted
- ✅ No errors in server logs
- ✅ Database connections are stable
- ✅ Package card generation works perfectly

### 🆘 **Emergency Contacts & Rollback**

#### If Issues Occur:
1. **Check server logs** immediately
2. **Verify DNS resolution**
3. **Test database connectivity**
4. **Check SSL certificate status**

#### Rollback Procedure:
```bash
# Restore from backup
sudo cp -r /var/www/html.backup.YYYYMMDD /var/www/html
sudo systemctl reload nginx
```

#### Support Resources:
- Linode Documentation: https://www.linode.com/docs/
- Nginx Documentation: https://nginx.org/en/docs/
- Certbot Documentation: https://certbot.eff.org/docs/

### 🎉 **Deployment Complete!**

Once all items are checked off, your TripXplo Family EMI website will be live at:
**https://family.tripxplo.com**

Congratulations on your successful deployment! 🚀
