# TripXplo Family EMI - Database Integration Plan for family.tripxplo.com

## **🎯 INTEGRATION OVERVIEW**

Transform the static frontend into a fully functional database-driven website that connects to your existing Quote Generator system and family type pricing database.

---

## **📊 DATABASE ARCHITECTURE**

### **Existing Database Structure:**
```
🏢 CRM Database (tlfwcnikdlwoliqzavxj.supabase.co)
├── family_type (34 family types)
├── User management
└── Master data

💰 Quote Database (lkqbrlrmrsnbtkoryazq.supabase.co)
├── quotes (baseline quotes)
├── quote_mappings (pricing data)
├── family_type_prices (calculated prices)
├── family_type_emi_plans (EMI options)
└── public_family_quotes (website leads)
```

### **Integration Points:**
1. **Family Type Detection** → `family_type` table
2. **Package Search** → `family_type_prices` table
3. **EMI Options** → `family_type_emi_plans` table
4. **Lead Capture** → `public_family_quotes` table

---

## **🔧 BACKEND API LAYER**

### **Required API Endpoints:**

#### **1. Family Types API**
```typescript
GET /api/family-types
// Returns all 34 family types with composition details
Response: {
  success: true,
  data: [
    {
      family_id: "BB",
      family_type: "Baby Bliss",
      no_of_adults: 2,
      no_of_infants: 1,
      no_of_children: 0,
      no_of_child: 0,
      family_count: 3,
      composition: "2 Adults + 1 Infant (Below 2 yrs)"
    }
  ]
}
```

#### **2. Destinations API**
```typescript
GET /api/destinations
// Returns unique destinations from family_type_prices
Response: {
  success: true,
  data: [
    {
      destination: "Kashmir",
      category: "Hill Station",
      packages_available: 15,
      price_range: { min: 25000, max: 85000 }
    }
  ]
}
```

#### **3. Package Search API**
```typescript
POST /api/search-packages
Body: {
  destination: "Kashmir",
  travel_date: "2024-12",
  adults: 2,
  children: 0,
  infants: 0
}

Response: {
  success: true,
  matched_family_type: {
    family_id: "SD",
    family_type: "Stellar Duo",
    composition: "2 Adults"
  },
  packages: [
    {
      id: "uuid",
      title: "Kashmir Winter Wonderland",
      duration_days: 6,
      total_price: 45000,
      emi_options: [
        {
          months: 3,
          monthly_amount: 15000,
          total_amount: 45000,
          processing_fee: 0,
          label: "Quick Pay"
        }
      ],
      inclusions: ["Flights", "Hotels", "Meals", "Sightseeing"],
      images: ["url1", "url2"]
    }
  ]
}
```

#### **4. Package Details API**
```typescript
GET /api/packages/:id
Response: {
  success: true,
  package: {
    id: "uuid",
    title: "Kashmir Winter Wonderland",
    description: "Experience the beauty of Kashmir...",
    duration_days: 6,
    itinerary: [
      {
        day: 1,
        title: "Arrival in Srinagar",
        description: "Airport pickup, houseboat check-in..."
      }
    ],
    inclusions: ["Round-trip flights", "4-star hotels"],
    exclusions: ["Personal expenses", "Travel insurance"],
    emi_plans: [...],
    images: [...]
  }
}
```

#### **5. Lead Capture API**
```typescript
POST /api/quote-request
Body: {
  customer_email: "<EMAIL>",
  customer_phone: "+91XXXXXXXXXX",
  customer_name: "John Doe",
  destination: "Kashmir",
  travel_date: "2024-12-15",
  adults: 2,
  children: 0,
  infants: 0,
  selected_package_id: "uuid",
  selected_emi_plan_id: "uuid",
  utm_source: "google",
  session_id: "sess_123"
}

Response: {
  success: true,
  quote_id: "uuid",
  message: "Quote request submitted successfully"
}
```

---

## **💻 FRONTEND INTEGRATION**

### **1. Environment Configuration**
```typescript
// src/nest/config/environment.ts
export const config = {
  API_BASE_URL: process.env.NODE_ENV === 'production' 
    ? 'https://family.tripxplo.com/api'
    : 'http://localhost:3000/api',
  
  SUPABASE_URL: 'https://lkqbrlrmrsnbtkoryazq.supabase.co',
  SUPABASE_ANON_KEY: 'your-anon-key',
  
  // Database connections
  CRM_DB_URL: 'https://tlfwcnikdlwoliqzavxj.supabase.co',
  QUOTE_DB_URL: 'https://lkqbrlrmrsnbtkoryazq.supabase.co'
};
```

### **2. API Service Layer**
```typescript
// src/nest/services/apiService.ts
class FamilyEMIApiService {
  private baseURL: string;
  
  constructor() {
    this.baseURL = config.API_BASE_URL;
  }
  
  async getFamilyTypes() {
    const response = await fetch(`${this.baseURL}/family-types`);
    return response.json();
  }
  
  async getDestinations() {
    const response = await fetch(`${this.baseURL}/destinations`);
    return response.json();
  }
  
  async searchPackages(searchParams: SearchParams) {
    const response = await fetch(`${this.baseURL}/search-packages`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(searchParams)
    });
    return response.json();
  }
  
  async getPackageDetails(packageId: string) {
    const response = await fetch(`${this.baseURL}/packages/${packageId}`);
    return response.json();
  }
  
  async submitQuoteRequest(quoteData: QuoteRequest) {
    const response = await fetch(`${this.baseURL}/quote-request`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(quoteData)
    });
    return response.json();
  }
}

export const apiService = new FamilyEMIApiService();
```

### **3. Updated JavaScript Integration**
```typescript
// Replace static data with API calls
async function loadFamilyTypes() {
  try {
    const response = await apiService.getFamilyTypes();
    if (response.success) {
      familyTypesData = response.data;
      updateFamilyTypeDisplay();
    }
  } catch (error) {
    console.error('Error loading family types:', error);
  }
}

async function loadDestinations() {
  try {
    const response = await apiService.getDestinations();
    if (response.success) {
      destinations = response.data.map(d => d.destination);
      setupDestinationAutocomplete();
    }
  } catch (error) {
    console.error('Error loading destinations:', error);
  }
}

async function searchPackages() {
  const searchParams = {
    destination: document.getElementById('destination').value,
    travel_date: document.getElementById('travelDate').value,
    adults: travelers.adults,
    children: travelers.children,
    infants: travelers.infants
  };
  
  try {
    showLoading(true);
    const response = await apiService.searchPackages(searchParams);
    
    if (response.success) {
      displaySearchResults(response);
    } else {
      showError('No packages found for your search criteria');
    }
  } catch (error) {
    console.error('Error searching packages:', error);
    showError('Failed to search packages. Please try again.');
  } finally {
    showLoading(false);
  }
}
```

---

## **🏗️ BACKEND IMPLEMENTATION**

### **Technology Stack Options:**

#### **Option 1: Node.js + Express (Recommended)**
```typescript
// server.js
import express from 'express';
import cors from 'cors';
import { createClient } from '@supabase/supabase-js';

const app = express();
app.use(cors());
app.use(express.json());

// Database connections
const crmDB = createClient(CRM_DB_URL, CRM_ANON_KEY);
const quoteDB = createClient(QUOTE_DB_URL, QUOTE_ANON_KEY);

// API Routes
app.get('/api/family-types', async (req, res) => {
  try {
    const { data, error } = await crmDB
      .from('family_type')
      .select('*')
      .order('family_type');
    
    if (error) throw error;
    
    res.json({ success: true, data });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.post('/api/search-packages', async (req, res) => {
  try {
    const { destination, adults, children, infants } = req.body;
    
    // 1. Detect family type
    const familyType = await detectFamilyType(adults, children, infants);
    
    // 2. Search packages
    const { data: packages, error } = await quoteDB
      .from('family_type_prices')
      .select(`
        *,
        family_type_emi_plans(*)
      `)
      .eq('destination', destination)
      .eq('family_type_id', familyType.family_id)
      .eq('is_public_visible', true);
    
    if (error) throw error;
    
    res.json({ 
      success: true, 
      matched_family_type: familyType,
      packages: formatPackagesForFrontend(packages)
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.listen(3000, () => {
  console.log('Family EMI API server running on port 3000');
});
```

#### **Option 2: Serverless Functions (Vercel/Netlify)**
```typescript
// api/search-packages.ts
import { VercelRequest, VercelResponse } from '@vercel/node';
import { createClient } from '@supabase/supabase-js';

export default async function handler(req: VercelRequest, res: VercelResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  try {
    const { destination, adults, children, infants } = req.body;
    
    const quoteDB = createClient(
      process.env.QUOTE_DB_URL!,
      process.env.QUOTE_DB_ANON_KEY!
    );
    
    // Implementation similar to Express version
    
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
}
```

---

## **🚀 DEPLOYMENT STRATEGY**

### **1. Subdomain Setup (family.tripxplo.com)**
```nginx
# Nginx configuration
server {
    listen 80;
    server_name family.tripxplo.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name family.tripxplo.com;
    
    # SSL configuration
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    
    # Serve static files
    location / {
        root /var/www/family-emi;
        try_files $uri $uri/ /index.html;
    }
    
    # API proxy
    location /api/ {
        proxy_pass http://localhost:3000/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### **2. Environment Variables**
```bash
# .env.production
NODE_ENV=production
API_BASE_URL=https://family.tripxplo.com/api

# Database connections
CRM_DB_URL=https://tlfwcnikdlwoliqzavxj.supabase.co
CRM_ANON_KEY=your-crm-anon-key
QUOTE_DB_URL=https://lkqbrlrmrsnbtkoryazq.supabase.co
QUOTE_ANON_KEY=your-quote-anon-key

# Security
JWT_SECRET=your-jwt-secret
CORS_ORIGIN=https://family.tripxplo.com
```

### **3. Build Process**
```json
{
  "scripts": {
    "build": "npm run build:frontend && npm run build:backend",
    "build:frontend": "webpack --mode=production",
    "build:backend": "tsc && cp -r dist/* /var/www/family-emi/api/",
    "deploy": "npm run build && systemctl restart family-emi-api"
  }
}
```

---

## **📈 NEXT STEPS**

### **Phase 1: Backend API Development (Week 1)**
1. Set up Node.js/Express server
2. Implement database connections
3. Create API endpoints
4. Test with existing data

### **Phase 2: Frontend Integration (Week 2)**
1. Replace static data with API calls
2. Add loading states and error handling
3. Implement form validation
4. Test user flows

### **Phase 3: Deployment & Testing (Week 3)**
1. Set up subdomain hosting
2. Configure SSL certificates
3. Deploy and test in production
4. Performance optimization

### **Phase 4: Enhancement (Week 4)**
1. Add analytics tracking
2. Implement SEO optimization
3. Add email notifications
4. Performance monitoring

**Ready to transform your static website into a fully functional, database-driven Family EMI platform! 🚀**
