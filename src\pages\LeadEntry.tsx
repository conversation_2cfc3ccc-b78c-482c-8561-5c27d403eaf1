import React, { useState, useEffect } from 'react';
import { createLead, LeadData } from '../lib/leadService';
import { supabase } from '../lib/supabaseClient';
import { useAuth } from '../contexts/AuthContext';
import { LEAD_STATUSES, isValidLeadStatus } from '../constants/leadStatus';

const LeadEntry: React.FC = () => {
  const { user } = useAuth();
  
  const initialState: LeadData = {
    customer_name: '',
    email: '',
    phone: '',
    destination: '',
    departure_city: '',
    travel_date: '',
    adults: 1,
    children: 0,
    infants: 0,
    nights: 1,
    budget_range: '',
    lead_source: 'instagram',
    priority: 'low',
    notes: '',
    special_requests: '',
    status: LEAD_STATUSES[0], // Default to 'NEW LEAD'
  };

  const [formData, setFormData] = useState<LeadData>(initialState);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [pastedText, setPastedText] = useState<string>('');
  const [authStatus, setAuthStatus] = useState<string>('Checking...');

  // Check authentication status on component mount
  useEffect(() => {
    async function checkAuth() {
      try {
        console.log('Checking authentication status...');
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Auth check error:', error);
          setAuthStatus('Error: ' + error.message);
          return;
        }
        
        console.log('Auth session result:', data);
        
        if (data.session) {
          setAuthStatus('Authenticated as: ' + data.session.user.email);
          console.log('User is authenticated:', data.session.user);
        } else {
          setAuthStatus('Not authenticated');
          console.log('User is not authenticated');
        }
      } catch (err) {
        console.error('Exception during auth check:', err);
        setAuthStatus('Auth check failed');
      }
    }
    
    checkAuth();
  }, []);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, type, value } = e.target as any;
    
    // Handle numbers specially to avoid NaN issues
    if (type === 'number') {
      // If the input is cleared (empty string), use default values instead of NaN
      const parsedValue = value === '' ? 0 : parseInt(value, 10);
      // Use null check to handle both NaN and undefined cases
      const validValue = isNaN(parsedValue) ? 0 : parsedValue;
      
      setFormData((prev) => ({
        ...prev,
        [name]: validValue,
      }));
    } else {
      // For non-numeric inputs, just use the value as-is
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submission started');
    setError(null);
    setSuccess(null);
    
    // Check authentication via AuthContext
    if (!user) {
      setError('You must be logged in to submit a lead.');
      return;
    }
    console.log('Authentication verified for user:', user.id);
    
    // Validate required fields
    if (!formData.customer_name || !formData.phone || !formData.destination) {
      console.error('Validation failed: required fields missing');
      setError('Please fill in all required fields (Customer Name, Phone, Destination).');
      return;
    }

    // Validate status
    if (!isValidLeadStatus(formData.status)) {
      setError('Invalid status selected.');
      return;
    }

    setIsSubmitting(true);
    
    try {
      console.log('Creating lead with user ID:', user.id);
      const result = await createLead(formData, user.id);
      console.log('Response from createLead:', result);
      
      if (result.success) {
        setSuccess('Lead created successfully!');
        setFormData(initialState);
        setPastedText('');
      } else {
        // Show detailed error info in UI for debugging
        let errorMessage = 'Unknown error';
        if (result.error) {
          if (typeof result.error === 'string') {
            errorMessage = result.error;
          } else if (result.error.message) {
            errorMessage = result.error.message;
          } else {
            errorMessage = JSON.stringify(result.error);
          }
        }
        setError('Error creating lead: ' + errorMessage);
      }
    } catch (error: any) {
      console.error('Exception caught during lead creation:', error);
      setError('Error creating lead: ' + (error.message || error.toString() || 'Unknown error'));
    } finally {
      setIsSubmitting(false);
    }
    
    console.log('Form submission completed');
  };

  // Helper to parse pasted customer detail text and return partial LeadData
  const parsePastedText = (text: string): Partial<LeadData> => {
    // Month name to index map (0-based)
    const monthMap: Record<string, number> = {
      january: 0, jan: 0,
      february: 1, feb: 1,
      march: 2, mar: 2,
      april: 3, apr: 3,
      may: 4,
      june: 5, jun: 5,
      july: 6, jul: 6,
      august: 7, aug: 7,
      september: 8, sept: 8, sep: 8,
      october: 9, oct: 9,
      november: 10, nov: 10,
      december: 11, dec: 11
    };

    // Convert raw date formats like "March 15", "15 March", or with suffixes to ISO YYYY-MM-DD
    const parseDate = (raw: string): string | undefined => {
      // Support numeric day/month/year format like "25/05/2025"
      const slashMatch = raw.match(/(\d{1,2})\/(\d{1,2})\/(\d{4})/);
      if (slashMatch) {
        const d = parseInt(slashMatch[1], 10);
        const m = parseInt(slashMatch[2], 10);
        const y = parseInt(slashMatch[3], 10);
        const dd = String(d).padStart(2, '0');
        const mm = String(m).padStart(2, '0');
        return `${y}-${mm}-${dd}`;
      }
      const cleaned = raw.toLowerCase().replace(/,/g, '').replace(/(st|nd|rd|th)$/i, '');
      const parts = cleaned.split(/\s+/);
      let day: number | undefined;
      let monthIdx: number | undefined;
      let year: number | undefined;
      parts.forEach((part) => {
        const num = parseInt(part, 10);
        if (!isNaN(num)) {
          if (num > 31) year = num;
          else day = num;
        } else {
          monthIdx = monthMap[part] ?? monthIdx;
        }
      });
      if (monthIdx !== undefined && day !== undefined) {
        if (!year) year = new Date().getFullYear();
        return `${year}-${String(monthIdx + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      }
    };

    const patterns: { key: keyof LeadData; regex: RegExp }[] = [
      { key: 'travel_date', regex: /(Travel\s?Date|Date)\s?:\s?(.*)/i },
      { key: 'destination', regex: /(Destination)\s?:\s?(.*)/i },
      { key: 'adults', regex: /(No\.?\s?of\s?People)\s?:\s?(.*)/i },
      { key: 'nights', regex: /(No\.?\s?of\s?Nights|Nights)\s?:\s?(.*)/i },
      { key: 'departure_city', regex: /(Departure\s?City|Departure)\s?:\s?(.*)/i },
      { key: 'phone', regex: /(Mobile\s?No\.?|Mobile|Mobil)\s?:\s?(.*)/i },
      { key: 'email', regex: /(Mail\s?id|Mail\s?I['']d|Mail|Email)\s?:\s?(.*)/i },
      { key: 'customer_name', regex: /(Customer\s?Name|Name)\s?:\s?(.*)/i },
      { key: 'children', regex: /(Children)\s?:\s?(.*)/i },
      { key: 'infants', regex: /(Infants)\s?:\s?(.*)/i },
      { key: 'budget_range', regex: /(Budget\s?Range|Budget)\s?:\s?(.*)/i },
      { key: 'notes', regex: /(Notes?)\s?:\s?(.*)/i },
    ];

    const result: Partial<LeadData> = {};
    patterns.forEach(({ key, regex }) => {
      // Extract the first matching line for this key
      const lines = text.split(/\r?\n/);
      let rawValue: string | undefined;
      for (const line of lines) {
        const m = line.match(regex);
        if (m && m[2]) {
          rawValue = m[2].trim();
          break;
        }
      }
      if (!rawValue) return;
      let value: any;
      switch (key) {
        case 'travel_date': {
          const iso = parseDate(rawValue);
          if (iso) {
            value = iso;
          }
          break;
        }
        case 'adults':
        case 'nights':
        case 'children':
        case 'infants': {
          const parsed = parseInt(rawValue, 10);
          value = isNaN(parsed) ? 0 : parsed;
          break;
        }
        default:
          value = rawValue;
      }
      if (value !== undefined) {
        result[key] = value;
      }
    });
    return result;
  };

  return (
    <div>
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <h1 className="text-primary text-2xl font-bold">New Lead Entry</h1>
          <p className="text-gray-600 text-sm mt-1">Add a new travel inquiry to your leads database</p>
          <p className="text-xs mt-1 font-medium" style={{ color: authStatus.includes('Authenticated') ? 'green' : 'red' }}>
            {authStatus}
          </p>
        </div>
        <div className="p-6">
          {error && <div className="text-red-500 mb-4 p-3 bg-red-50 rounded">{error}</div>}
          {success && <div className="text-green-500 mb-4 p-3 bg-green-50 rounded">{success}</div>}
          
          <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-6 md:col-span-2">
              {/* Customer Information */}
              <div className="mb-6">
                <h2 className="text-lg font-medium text-primary-dark mb-4 pb-2 border-b border-gray-200">
                  Customer Information
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="customer_name" className="block text-primary-dark font-medium mb-1">
                      Customer Name*
                    </label>
                    <input
                      type="text"
                      id="customer_name"
                      name="customer_name"
                      value={formData.customer_name}
                      onChange={handleChange}
                      required
                      className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                    />
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-primary-dark font-medium mb-1">
                      Phone*
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      required
                      className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                    />
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-primary-dark font-medium mb-1">
                      Email
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                    />
                  </div>
                </div>
              </div>

              {/* Trip Details */}
              <div className="mb-6">
                <h2 className="text-lg font-medium text-primary-dark mb-4 pb-2 border-b border-gray-200">
                  Trip Details
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="destination" className="block text-primary-dark font-medium mb-1">
                      Destination*
                    </label>
                    <input
                      type="text"
                      id="destination"
                      name="destination"
                      value={formData.destination}
                      onChange={handleChange}
                      required
                      className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                    />
                  </div>

                  <div>
                    <label htmlFor="departure_city" className="block text-primary-dark font-medium mb-1">
                      Departure City
                    </label>
                    <input
                      type="text"
                      id="departure_city"
                      name="departure_city"
                      value={formData.departure_city}
                      onChange={handleChange}
                      className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                    />
                  </div>

                  <div>
                    <label htmlFor="travel_date" className="block text-primary-dark font-medium mb-1">
                      Travel Date
                    </label>
                    <input
                      type="date"
                      id="travel_date"
                      name="travel_date"
                      value={formData.travel_date}
                      onChange={handleChange}
                      className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                    />
                  </div>

                  <div>
                    <label htmlFor="nights" className="block text-primary-dark font-medium mb-1">
                      Nights
                    </label>
                    <input
                      type="number"
                      id="nights"
                      name="nights"
                      min={1}
                      value={formData.nights}
                      onChange={handleChange}
                      className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                  <div>
                    <label htmlFor="adults" className="block text-primary-dark font-medium mb-1">
                      Adults
                    </label>
                    <input
                      type="number"
                      id="adults"
                      name="adults"
                      min={1}
                      value={formData.adults}
                      onChange={handleChange}
                      className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                    />
                  </div>
                  <div>
                    <label htmlFor="children" className="block text-primary-dark font-medium mb-1">
                      Children
                    </label>
                    <input
                      type="number"
                      id="children"
                      name="children"
                      min={0}
                      value={formData.children}
                      onChange={handleChange}
                      className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                    />
                  </div>
                  <div>
                    <label htmlFor="infants" className="block text-primary-dark font-medium mb-1">
                      Infants
                    </label>
                    <input
                      type="number"
                      id="infants"
                      name="infants"
                      min={0}
                      value={formData.infants}
                      onChange={handleChange}
                      className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                    />
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="mb-6">
                <h2 className="text-lg font-medium text-primary-dark mb-4 pb-2 border-b border-gray-200">
                  Additional Information
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="budget_range" className="block text-primary-dark font-medium mb-1">
                      Budget Range
                    </label>
                    <input
                      type="text"
                      id="budget_range"
                      name="budget_range"
                      value={formData.budget_range}
                      onChange={handleChange}
                      placeholder="e.g., 50000-100000"
                      className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                    />
                  </div>

                  <div>
                    <label htmlFor="lead_source" className="block text-primary-dark font-medium mb-1">
                      Lead Source
                    </label>
                    <select
                      id="lead_source"
                      name="lead_source"
                      value={formData.lead_source}
                      onChange={handleChange}
                      className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                    >
                      <option value="instagram">Instagram</option>
                      <option value="whatsapp">WhatsApp</option>
                      <option value="website">Website</option>
                      <option value="referral">Referral</option>
                      <option value="partner">Partner</option>
                      <option value="direct">Direct</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="priority" className="block text-primary-dark font-medium mb-1">
                      Priority
                    </label>
                    <select
                      id="priority"
                      name="priority"
                      value={formData.priority}
                      onChange={handleChange}
                      className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="status" className="block text-primary-dark font-medium mb-1">
                      Status
                    </label>
                    <select
                      id="status"
                      name="status"
                      value={formData.status}
                      onChange={handleChange}
                      className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                    >
                      {LEAD_STATUSES.map((status) => (
                        <option key={status} value={status}>{status}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <div>
                    <label htmlFor="notes" className="block text-primary-dark font-medium mb-1">
                      Notes
                    </label>
                    <textarea
                      id="notes"
                      name="notes"
                      value={formData.notes}
                      onChange={handleChange}
                      rows={3}
                      placeholder="Any additional notes about the lead..."
                      className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                    />
                  </div>

                  <div>
                    <label htmlFor="special_requests" className="block text-primary-dark font-medium mb-1">
                      Special Requests
                    </label>
                    <textarea
                      id="special_requests"
                      name="special_requests"
                      value={formData.special_requests}
                      onChange={handleChange}
                      rows={3}
                      placeholder="Any special requirements or requests..."
                      className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                    />
                  </div>
                </div>
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className={`w-full py-3 px-4 bg-primary hover:bg-primary-dark text-white font-bold rounded focus:outline-none focus:shadow-outline transition-colors ${
                  isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isSubmitting ? 'Creating Lead...' : 'Create Lead'}
              </button>
            </div>

            {/* Paste and Parse Section */}
            <div className="space-y-4 md:col-span-1 bg-gray-50 p-4 rounded border">
              <h3 className="text-lg font-medium text-primary-dark mb-2">Quick Fill</h3>
              <label htmlFor="pastedText" className="block text-primary-dark font-medium mb-1">
                Paste lead details here
              </label>
              <textarea
                id="pastedText"
                rows={12}
                value={pastedText}
                onChange={(e) => setPastedText(e.target.value)}
                placeholder="Paste lead details here...&#10;&#10;Example format:&#10;Customer Name: John Doe&#10;Phone: 9876543210&#10;Email: <EMAIL>&#10;Destination: Goa&#10;Travel Date: March 15&#10;Adults: 2&#10;Nights: 3"
                className="mt-1 block w-full border border-primary-light rounded px-3 py-2 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary text-sm"
              />
              <button
                type="button"
                onClick={() => {
                  const parsedData = parsePastedText(pastedText);
                  console.log('Parsed Data:', parsedData);
                  setFormData(prev => ({ ...prev, ...parsedData }));
                  setPastedText('');
                }}
                disabled={!pastedText.trim()}
                className={`w-full py-2 px-4 bg-primary hover:bg-primary-dark text-white font-bold rounded focus:outline-none focus:shadow-outline transition-colors ${
                  !pastedText.trim() ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                Parse & Fill Form
              </button>
              <p className="text-xs text-gray-600 mt-2">
                This will automatically fill the form fields based on the pasted text. You can then review and modify before submitting.
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LeadEntry; 