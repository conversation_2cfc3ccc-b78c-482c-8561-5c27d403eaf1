#!/bin/bash

echo "🔍 Checking family.tripxplo.com deployment status..."
echo "=================================================="

# Check if site is accessible
echo "1. Checking site accessibility..."
curl -I http://family.tripxplo.com 2>/dev/null | head -1

# Check if HTTPS is working
echo "2. Checking HTTPS..."
curl -I https://family.tripxplo.com 2>/dev/null | head -1

# Try to connect to server and check files
echo "3. Attempting to check server files..."
ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no root@************* '
    echo "Connected to server successfully!"
    echo "Files in /var/www/family/:"
    ls -la /var/www/family/ | head -10
    echo ""
    echo "Checking nginx status:"
    systemctl status nginx --no-pager -l | head -5
    echo ""
    echo "Checking nginx configuration for family subdomain:"
    ls -la /etc/nginx/sites-available/ | grep -i family || echo "No family config in sites-available"
    ls -la /etc/nginx/sites-enabled/ | grep -i family || echo "No family config in sites-enabled"
    echo ""
    echo "Checking if family directory is properly configured:"
    cat /etc/nginx/sites-available/default | grep -A 10 -B 5 family || echo "No family configuration found in default"
' 2>/dev/null || echo "SSH connection failed"

echo ""
echo "4. Checking DNS resolution..."
nslookup family.tripxplo.com

echo ""
echo "Deployment check completed!"
