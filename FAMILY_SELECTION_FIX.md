# Family Selection UI Fix - Correct Field Mapping

## Problem Identified

The family selection popup had incorrect field mappings and age ranges that didn't match the database schema:

### ❌ **Before (Incorrect):**
- **Adults** (12+ years) → `no_of_adults` ✅ (Correct)
- **Children** (2-11 years) → `no_of_children` ❌ (Wrong age range)
- **Infants** (Under 2 years) → `no_of_infants` ✅ (Correct)

### ✅ **After (Correct):**
- **Adults** (12+ years) → `no_of_adults`
- **Child below 5** (2-5 years) → `no_of_child`
- **Children** (6-11 years) → `no_of_children`
- **Infants** (Under 2 years) → `no_of_infants`

## Database Schema Alignment

The fix aligns with the actual database table structure:

```sql
-- family_types table fields
no_of_adults    INTEGER  -- Adults (12+ years)
no_of_child     INTEGER  -- Child below 5 (2-5 years)
no_of_children  INTEGER  -- Children (6-11 years)
no_of_infants   INTEGER  -- Infants (Under 2 years)
```

## Files Modified

### 1. **src/nest/index.html**

#### Traveler Selection Modal:
```html
<!-- Added new field for Child below 5 -->
<div class="counter-group">
  <div class="counter-info">
    <span class="counter-label">Child below 5</span>
    <span class="counter-sublabel">(2-5 years)</span>
  </div>
  <div class="counter-controls">
    <button type="button" class="counter-btn" onclick="updateCounter('child', -1)">-</button>
    <span class="counter-value" id="childCount">0</span>
    <button type="button" class="counter-btn" onclick="updateCounter('child', 1)">+</button>
  </div>
</div>

<!-- Updated Children age range -->
<div class="counter-group">
  <div class="counter-info">
    <span class="counter-label">Children</span>
    <span class="counter-sublabel">(6-11 years)</span> <!-- Changed from (2-11 years) -->
  </div>
  <!-- ... -->
</div>
```

#### JavaScript Updates:
```javascript
// Updated travelers object
let travelers = { adults: 2, child: 0, children: 0, infants: 0 };

// Updated search parameters
const searchParams = {
  destination,
  travel_date: travelDate,
  adults: travelers.adults,
  child: travelers.child,        // New field
  children: travelers.children,
  infants: travelers.infants
};

// Updated family type detection
function detectFamilyType() {
  const { adults, child, children, infants } = travelers;
  
  // Find exact match with correct field mapping
  let match = familyTypesData.find(ft =>
    ft.no_of_adults === adults &&
    ft.no_of_child === child &&      // New field
    ft.no_of_children === children &&
    ft.no_of_infants === infants
  );
}

// Updated display function
function updateFamilyTypeDisplay() {
  const { adults, child, children, infants } = travelers;
  let displayText = `${adults} Adult${adults > 1 ? 's' : ''}`;
  if (child > 0) displayText += `, ${child} Child (2-5 yrs)`;
  if (children > 0) displayText += `, ${children} Children (6-11 yrs)`;
  if (infants > 0) displayText += `, ${infants} Infant${infants > 1 ? 's' : ''}`;
}
```

### 2. **src/nest/js/databaseService.js**

#### Updated searchPackages function:
```javascript
async searchPackages(searchParams) {
  const { destination, adults, child, children, infants } = searchParams;
  
  // Updated family type detection call
  const familyType = await this.detectFamilyType(adults, child || 0, children || 0, infants || 0);
}
```

#### Updated detectFamilyType function:
```javascript
async detectFamilyType(adults, child, children, infants) {
  // Find exact match with correct field mapping
  let match = familyTypes.find(ft =>
    ft.no_of_adults === adults &&
    ft.no_of_child === child &&
    ft.no_of_children === children &&
    ft.no_of_infants === infants
  );
}
```

#### Updated formatFamilyComposition function:
```javascript
formatFamilyComposition(familyType) {
  let composition = `${familyType.no_of_adults} Adult${familyType.no_of_adults > 1 ? 's' : ''}`;
  
  if (familyType.no_of_child > 0) {
    composition += ` + ${familyType.no_of_child} Child (2-5 yrs)`;
  }
  
  if (familyType.no_of_children > 0) {
    composition += ` + ${familyType.no_of_children} Children (6-11 yrs)`;
  }
  
  if (familyType.no_of_infants > 0) {
    composition += ` + ${familyType.no_of_infants} Infant${familyType.no_of_infants > 1 ? 's' : ''}`;
  }
  
  return composition;
}
```

#### Updated submitQuoteRequest function:
```javascript
async submitQuoteRequest(quoteData) {
  const { data, error } = await this.quoteDB
    .from('public_family_quotes')
    .insert({
      // ... other fields
      no_of_adults: quoteData.adults,
      no_of_child: quoteData.child || 0,      // New field
      no_of_children: quoteData.children || 0,
      no_of_infants: quoteData.infants || 0,
    });
}
```

## Example Usage

### Input Selection:
- **Adults**: 3
- **Child below 5**: 1  
- **Children**: 0
- **Infants**: 0

### Expected Output:
- **Family Type**: "Cosmic Combo Duo - 3 Adults + 1 Child (Below 5 yrs)"
- **Database Fields**: `{ no_of_adults: 3, no_of_child: 1, no_of_children: 0, no_of_infants: 0 }`
- **Display**: "3 Adults, 1 Child (2-5 yrs)"

## Testing

Created comprehensive test file:
- **src/nest/test-family-selection.html** - Interactive test for family selection

## Benefits

✅ **Accurate Matching**: Family types now match correctly with database records  
✅ **Proper Age Ranges**: Clear distinction between child (2-5) and children (6-11)  
✅ **Database Consistency**: All fields align with actual database schema  
✅ **Better UX**: Users can select exact family composition  
✅ **Correct Pricing**: Ferry costs and package pricing calculated accurately  

## Impact on Ferry Cost Calculation

With the corrected field mapping, ferry cost calculation now works properly:
```javascript
// Ferry cost = per-person cost * eligible persons (adults + children + child, excluding infants)
const ferryEligiblePersons = familyType.no_of_adults + 
                            (familyType.no_of_children || 0) + 
                            (familyType.no_of_child || 0);
const ferryCost = (mappingData.additional_costs.ferry_cost || 0) * ferryEligiblePersons;
```

This ensures that:
- Ferry cost per person is taken from Quote Mapping configuration
- Multiplied by eligible persons (adults + children + child)
- Infants (under 2) are excluded from ferry charges
- System uses only Quote Mapping data (no fallback to Quote Generator)

The system now correctly identifies family types like "Cosmic Combo Duo" and provides accurate package recommendations and pricing.
