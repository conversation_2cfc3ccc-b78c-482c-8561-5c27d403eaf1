import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { getQuoteSupabaseConfig } from '../../config/env';

// Singleton instance
let supabaseInstance: SupabaseClient | null = null;

/**
 * Creates and returns a Supabase client for the Quote module.
 * Uses a singleton pattern to prevent multiple instances.
 */
const createSupabaseClient = (): SupabaseClient => {
  // If we already have an instance, return it
  if (supabaseInstance) {
    return supabaseInstance;
  }

  // Load from environment configuration
  const quoteConfig = getQuoteSupabaseConfig();
  const supabaseUrl = quoteConfig.url;
  const supabaseAnonKey = quoteConfig.anonKey;

  console.log('[quoteSupabaseClient] Initializing Quote Supabase client with URL:', supabaseUrl);
  console.log('[quoteSupabaseClient] Initializing with Key:', supabaseAnonKey ? 'Exists' : 'MISSING!');

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('[quoteSupabaseClient] ERROR: Quote Supabase config missing! Please check your environment configuration.');
  }

  // Define Supabase client options with explicit auth settings and improved connection handling
  const supabaseOptions = {
    auth: {
      persistSession: true,     // Store session in local storage
      autoRefreshToken: true,   // Refresh token before expiry
      detectSessionInUrl: true, // Check URL for auth callbacks
      // Add a shorter storage key to avoid potential corruption of long keys
      storageKey: 'tripxplo-quote-supabase-auth', // Different storage key from CRM
      // Use 'pkce' flow type for better security
      flowType: 'pkce' as const,  // TypeScript needs 'as const' to narrow the type
    },
    realtime: {
      // Improve connection reliability with auto-reconnect settings
      params: {
        eventsPerSecond: 10,
      },
    },
    global: {
      fetch: (input: RequestInfo | URL, init?: RequestInit) => {
        // Add request timeout
        const timeoutController = new AbortController();
        // Use different timeouts for different types of requests
        const url = typeof input === 'string' ? input : input.toString();
        let timeoutDuration = 15000; // Default 15 seconds

        if (url.includes('/auth/v1/token')) {
          timeoutDuration = 30000; // 30 seconds for auth
        } else if (url.includes('quote_mappings')) {
          timeoutDuration = 25000; // 25 seconds for quote mappings operations
        } else if (url.includes('upsert') || url.includes('insert')) {
          timeoutDuration = 20000; // 20 seconds for write operations
        } else if (url.includes('select')) {
          timeoutDuration = 15000; // 15 seconds for read operations
        }

        const timeoutId = setTimeout(() => {
          console.log(`[quoteSupabaseClient] Request timeout for ${typeof input === 'string' ? input.toString().split('?')[0] : 'request'} after ${timeoutDuration}ms`);
          timeoutController.abort();
        }, timeoutDuration);

        // Use the abort controller signal
        const options = {
          ...init,
          signal: timeoutController.signal,
        };

        return fetch(input, options)
          .finally(() => clearTimeout(timeoutId));
      }
    }
  };

  // Initialize Supabase client
  console.log('[quoteSupabaseClient] Creating client with URL:', supabaseUrl);
  supabaseInstance = createClient(supabaseUrl, supabaseAnonKey, supabaseOptions);

  return supabaseInstance;
};

// Export the supabase client - lazy initialization
export const supabase = createSupabaseClient();

// Log when the module has fully loaded
console.log('[quoteSupabaseClient] Module initialization complete');

// Add connection status monitor
let connectionAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
let lastActiveTime = Date.now();
let keepAliveInterval: number | null = null;
let reconnectionInProgress = false;

// Function to check connection and reconnect if needed
const checkConnectionAndReconnect = async () => {
  if (reconnectionInProgress) return;

  try {
    reconnectionInProgress = true;
    console.log('[quoteSupabaseClient] Checking connection status...');

    // Use a timeout to prevent hanging
    const timeoutPromise = new Promise<{data: null, error: Error}>((_, reject) => {
      setTimeout(() => {
        reject({ data: null, error: new Error('Connection check timed out') });
      }, 8000); // 8 second timeout
    });

    // Race the session check against the timeout
    const { error } = await Promise.race([
      supabase.auth.getSession(),
      timeoutPromise
    ]);

    if (error) {
      console.warn('[quoteSupabaseClient] Connection check failed:', error.message);

      // Try to refresh the session first
      try {
        const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
        if (!refreshError && refreshData.session) {
          console.log('[quoteSupabaseClient] Session refreshed successfully');
          lastActiveTime = Date.now();
          connectionAttempts = 0;
          reconnectionInProgress = false;
          return;
        }
      } catch (refreshErr) {
        console.error('[quoteSupabaseClient] Error refreshing session:', refreshErr);
      }

      // If refresh failed and we haven't exceeded max attempts, try page reload
      if (connectionAttempts < MAX_RECONNECT_ATTEMPTS) {
        connectionAttempts++;
        console.log(`[quoteSupabaseClient] Attempting reconnection (${connectionAttempts}/${MAX_RECONNECT_ATTEMPTS})...`);

        // Only reload if user is logged in (has auth data in localStorage)
        if (localStorage.getItem('tripxplo-quote-supabase-auth')) {
          // Try to reconnect without full page reload first
          try {
            await supabase.auth.refreshSession();
            console.log('[quoteSupabaseClient] Session refreshed without page reload');
            lastActiveTime = Date.now();
          } catch (e) {
            console.warn('[quoteSupabaseClient] Session refresh failed, reloading page:', e);
            window.location.reload();
          }
        }
      } else if (connectionAttempts === MAX_RECONNECT_ATTEMPTS) {
        console.error('[quoteSupabaseClient] Max reconnection attempts reached. Please refresh the page manually.');
        // Show a user-friendly message
        if (typeof window !== 'undefined') {
          alert('Connection to the server was lost. Please refresh the page to reconnect.');
        }
      }
    } else {
      console.log('[quoteSupabaseClient] Connection verified');
      lastActiveTime = Date.now();
      connectionAttempts = 0;
    }
  } catch (e) {
    console.error('[quoteSupabaseClient] Error during connection check:', e);
  } finally {
    reconnectionInProgress = false;
  }
};

// Set up keep-alive ping to prevent idle timeouts
const setupKeepAlive = () => {
  if (keepAliveInterval) {
    clearInterval(keepAliveInterval);
  }

  // Ping every 4 minutes to keep the connection alive
  keepAliveInterval = setInterval(() => {
    const idleTime = Date.now() - lastActiveTime;

    // If idle for more than 3 minutes, send a keep-alive ping
    const ping = async () => {
      if (idleTime > 3 * 60 * 1000) {
        console.log('[quoteSupabaseClient] Sending keep-alive ping after idle period');
        try {
          // Simple ping that doesn't require auth
          await supabase.from('quotes').select('count', { count: 'exact', head: true });
          lastActiveTime = Date.now();
          console.log('[quoteSupabaseClient] Keep-alive ping successful');
        } catch (err: any) {
          console.warn('[quoteSupabaseClient] Keep-alive ping failed:', err.message);
          checkConnectionAndReconnect();
        }
      }
    };
    ping();
  }, 4 * 60 * 1000) as unknown as number; // 4 minutes
};

// Start the keep-alive mechanism
setupKeepAlive();

// Monitor for connection status changes
document.addEventListener('visibilitychange', () => {
  // When tab becomes visible again after being hidden
  if (document.visibilityState === 'visible') {
    console.log('[quoteSupabaseClient] Tab is now visible, checking connection...');

    // Check how long the tab was hidden
    const timeSinceActive = Date.now() - lastActiveTime;

    // If hidden for more than 1 minute, check connection
    if (timeSinceActive > 60 * 1000) {
      checkConnectionAndReconnect();
    } else {
      console.log('[quoteSupabaseClient] Tab was only hidden briefly, skipping connection check');
      lastActiveTime = Date.now();
    }
  }
});

// Add page reload/beforeunload handler to clean up connections properly
window.addEventListener('beforeunload', () => {
  console.log('[quoteSupabaseClient] Page unloading, closing connections gracefully...');
  // No need to wait for this to complete since the page is unloading
  // But it helps Supabase know this is an intentional disconnect
  supabase.removeAllChannels();

  // Clear any intervals
  if (keepAliveInterval) {
    clearInterval(keepAliveInterval);
    keepAliveInterval = null;
  }
});

// Run connectivity test AFTER the module has fully initialized
// This ensures it doesn't block other operations
// setTimeout(() => {
//   console.log('[quoteSupabaseClient] Starting delayed connectivity test...');

//   // Create a proper timeout for the fetch operation
//   const testPromise = supabase.from('quotes').select('id').limit(1);

//   // Track timeout ID separately instead of storing it on the promise
//   let timeoutId: number | null = null;

//   const timeoutPromise = new Promise<never>((_, reject) => {
//     timeoutId = setTimeout(() => {
//       reject(new Error('Connectivity test timed out after 15 seconds'));
//     }, 15000) as unknown as number; // 15 second timeout (reduced from 20)
//   });

//   // Race the test against a timeout, but don't block module initialization
//   Promise.race([testPromise, timeoutPromise])
//     .then((result: any) => {
//       // Clear timeout if the test completes
//       if (timeoutId) {
//         clearTimeout(timeoutId);
//         timeoutId = null;
//       }

//       const { data, error } = result;
//       if (error) {
//         console.error('[quoteSupabaseClient] Connectivity test error:', error.message);
//         console.log('[quoteSupabaseClient] Continuing despite connectivity test error');
//         // Try one more time with a different table that might be more responsive
//         console.log('[quoteSupabaseClient] Trying alternative connectivity test...');
//         return supabase.from('family_type').select('count', { count: 'exact', head: true });
//       } else {
//         console.log('[quoteSupabaseClient] Connectivity test successful:', { data });
//         lastActiveTime = Date.now(); // Update last active time on successful connection
//         return Promise.resolve({ success: true, data: null, error: null });
//       }
//     })
//     .then((result: any) => {
//       if (result && result.success === true) return; // Skip if first test was successful

//       const { data, error } = result;
//       if (error) {
//         console.error('[quoteSupabaseClient] Alternative connectivity test error:', error.message);
//         console.log('[quoteSupabaseClient] Continuing despite alternative connectivity test error');
//       } else {
//         console.log('[quoteSupabaseClient] Alternative connectivity test successful:', { data });
//         lastActiveTime = Date.now(); // Update last active time on successful connection
//       }
//     })
//     .catch((err: Error) => {
//       // Also clear timeout on error
//       if (timeoutId) {
//         clearTimeout(timeoutId);
//         timeoutId = null;
//       }
//       console.error('[quoteSupabaseClient] Connectivity test failed:', err.message);
//       console.log('[quoteSupabaseClient] Continuing despite connectivity test failure');

//       // Try a simpler health check that doesn't require database access
//       supabase.auth.getSession()
//         .then(() => console.log('[quoteSupabaseClient] Auth service is still accessible'))
//         .catch(authErr => console.error('[quoteSupabaseClient] Auth service check failed:', authErr.message));

//       // No need to retry if we're continuing anyway
//     });
// }, 1500); // Increased delay to ensure module is fully loaded

// Add event listener for network status changes
window.addEventListener('online', () => {
  console.log('[quoteSupabaseClient] Network connection restored, checking Supabase connection...');
  checkConnectionAndReconnect();
});

window.addEventListener('offline', () => {
  console.log('[quoteSupabaseClient] Network connection lost, will check connection when back online');
});

export async function getFamilyType(familyTypeId: string): Promise<any> {
  const { data, error: _error } = await supabase
    .from('family_types')
    .select('*')
    .eq('id', familyTypeId)
    .single();
  if (_error) throw _error;
  return data;
}

export async function getQuoteMapping(quoteId: string): Promise<any> {
  const { data, error: _error } = await supabase
    .from('quote_mappings')
    .select('*')
    .eq('quote_id', quoteId)
    .single();
  // No error thrown if not found, just returns null data
  return data;
}

export async function saveQuoteMapping(mappingData: any): Promise<{ success: boolean; data: any; error: any }> {
  try {
    const { id, ...dataToUpsert } = mappingData;

    const { data, error: _error } = await supabase
      .from('quote_mappings')
      .upsert({ id, ...dataToUpsert })
      .select();

    if (_error) {
      throw _error;
    }
    return { success: true, data, error: null };
  } catch (error) {
    return { success: false, data: null, error };
  }
}
