# TripXplo Family EMI - Deployment Guide for family.tripxplo.com

## **🚀 COMPLETE DEPLOYMENT SETUP**

This guide will help you deploy the Family EMI website to family.tripxplo.com with full database integration.

---

## **📋 PRE-DEPLOYMENT CHECKLIST**

### **1. Server Requirements**
- ✅ Ubuntu 20.04+ or CentOS 8+
- ✅ Node.js 16+ and npm 8+
- ✅ Nginx (for reverse proxy)
- ✅ SSL certificate for family.tripxplo.com
- ✅ PM2 (for process management)

### **2. Database Access**
- ✅ CRM Database: `tlfwcnikdlwoliqzavxj.supabase.co`
- ✅ Quote Database: `lkqbrlrmrsnbtkoryazq.supabase.co`
- ✅ Supabase API keys (anon keys)

### **3. Domain Setup**
- ✅ DNS A record: `family.tripxplo.com` → Server IP
- ✅ SSL certificate (Let's Encrypt or commercial)

---

## **🔧 STEP-BY-STEP DEPLOYMENT**

### **Step 1: Server Setup**

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18 LTS
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 globally
sudo npm install -g pm2

# Install Nginx
sudo apt install nginx -y

# Create application directory
sudo mkdir -p /var/www/family-emi
sudo chown $USER:$USER /var/www/family-emi
```

### **Step 2: Deploy Application Files**

```bash
# Navigate to application directory
cd /var/www/family-emi

# Copy your files (use SCP, Git, or FTP)
# Option 1: Using Git
git clone https://github.com/your-repo/tripxplo-family-emi.git .

# Option 2: Using SCP from local machine
# scp -r src/nest/* user@your-server:/var/www/family-emi/

# Install dependencies
npm install

# Copy environment file
cp .env.example .env
```

### **Step 3: Configure Environment**

```bash
# Edit environment file
nano .env
```

```env
# Production Configuration
NODE_ENV=production
PORT=3000
CORS_ORIGIN=https://family.tripxplo.com

# Database Configuration
CRM_DB_URL=https://tlfwcnikdlwoliqzavxj.supabase.co
CRM_ANON_KEY=your-actual-crm-anon-key

QUOTE_DB_URL=https://lkqbrlrmrsnbtkoryazq.supabase.co
QUOTE_ANON_KEY=your-actual-quote-anon-key

# Security
JWT_SECRET=your-secure-random-string-here
API_RATE_LIMIT=1000

# Logging
LOG_LEVEL=info
LOG_FILE=/var/log/family-emi.log
```

### **Step 4: Configure Nginx**

```bash
# Create Nginx configuration
sudo nano /etc/nginx/sites-available/family.tripxplo.com
```

```nginx
# Nginx Configuration for family.tripxplo.com
server {
    listen 80;
    server_name family.tripxplo.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name family.tripxplo.com;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/family.tripxplo.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/family.tripxplo.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Root directory for static files
    root /var/www/family-emi;
    index index.html;
    
    # Serve static files
    location / {
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API proxy
    location /api/ {
        proxy_pass http://localhost:3000/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;
}
```

```bash
# Enable the site
sudo ln -s /etc/nginx/sites-available/family.tripxplo.com /etc/nginx/sites-enabled/

# Test Nginx configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

### **Step 5: SSL Certificate Setup**

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Get SSL certificate
sudo certbot --nginx -d family.tripxplo.com

# Test auto-renewal
sudo certbot renew --dry-run
```

### **Step 6: Start Application with PM2**

```bash
# Create PM2 ecosystem file
nano ecosystem.config.js
```

```javascript
module.exports = {
  apps: [{
    name: 'family-emi-api',
    script: 'api/server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/var/log/family-emi-error.log',
    out_file: '/var/log/family-emi-out.log',
    log_file: '/var/log/family-emi.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

```bash
# Start application
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u $USER --hp $HOME
```

### **Step 7: Setup Monitoring and Logs**

```bash
# Create log directory
sudo mkdir -p /var/log
sudo chown $USER:$USER /var/log/family-emi*.log

# Setup log rotation
sudo nano /etc/logrotate.d/family-emi
```

```
/var/log/family-emi*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 $USER $USER
    postrotate
        pm2 reload family-emi-api
    endscript
}
```

---

## **🔍 TESTING DEPLOYMENT**

### **1. Health Check**
```bash
# Test API health
curl https://family.tripxplo.com/api/health

# Expected response:
# {"success":true,"message":"Family EMI API is running","timestamp":"..."}
```

### **2. Frontend Test**
```bash
# Test website loading
curl -I https://family.tripxplo.com

# Expected: HTTP/2 200 OK
```

### **3. Database Connection Test**
```bash
# Test family types API
curl https://family.tripxplo.com/api/family-types

# Expected: JSON with family types data
```

---

## **📊 MONITORING & MAINTENANCE**

### **PM2 Commands**
```bash
# Check application status
pm2 status

# View logs
pm2 logs family-emi-api

# Restart application
pm2 restart family-emi-api

# Monitor resources
pm2 monit
```

### **Nginx Commands**
```bash
# Check Nginx status
sudo systemctl status nginx

# Reload Nginx configuration
sudo nginx -s reload

# View access logs
sudo tail -f /var/log/nginx/access.log
```

### **SSL Certificate Renewal**
```bash
# Check certificate expiry
sudo certbot certificates

# Renew certificates
sudo certbot renew
```

---

## **🚨 TROUBLESHOOTING**

### **Common Issues:**

1. **API not responding**
   ```bash
   pm2 logs family-emi-api
   # Check for database connection errors
   ```

2. **SSL certificate issues**
   ```bash
   sudo certbot certificates
   sudo nginx -t
   ```

3. **Database connection errors**
   ```bash
   # Check environment variables
   cat .env
   # Verify Supabase keys
   ```

4. **High memory usage**
   ```bash
   pm2 monit
   # Restart if needed
   pm2 restart family-emi-api
   ```

---

## **🎯 POST-DEPLOYMENT CHECKLIST**

- ✅ Website loads at https://family.tripxplo.com
- ✅ API health check passes
- ✅ Family types load correctly
- ✅ Destination search works
- ✅ Package search returns results
- ✅ Quote request submission works
- ✅ SSL certificate is valid
- ✅ PM2 monitoring is active
- ✅ Logs are being written
- ✅ Auto-renewal is configured

**🎉 Your Family EMI website is now live on family.tripxplo.com!**
