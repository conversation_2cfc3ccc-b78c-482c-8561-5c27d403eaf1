<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TripXplo Family - Your Family Adventure, Planned & Paid Your Way</title>
    <meta name="description" content="Experience hassle-free family vacations with TripXplo's Family Prepaid EMI Packages. Discover, relax, and create lasting memories with flexible payment options." />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Volkhov:wght@400;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <link rel="stylesheet" href="globals.css" />
    <link rel="stylesheet" href="styleguide.css" />
    <link rel="stylesheet" href="style.css" />

    <!-- Supabase Client -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <!-- Configuration -->
    <script src="js/config.js"></script>

    <!-- API Service -->
    <script src="js/apiService.js"></script>

    <!-- Database Integration -->
    <script src="js/databaseService.js"></script>

    <!-- Package Card Generator -->
    <script src="js/packageCardGenerator.js"></script>
  </head>
  <body>
    <div class="family-emi-website">
      <!-- Hero Section with Search Form -->
      <section class="hero-section">
        <!-- Navigation -->
        <nav class="navigation">
          <div class="nav-container">
            <div class="logo">
              <img src="https://tripemilestone.in-maa-1.linodeobjects.com/logo%2Ftripxplo-logo-crop.png" alt="TripXplo" class="logo-image" />
            </div>
            <div class="nav-links">
              <a href="#packages">Packages</a>
              <a href="#about">About</a>
              <a href="#contact">Contact</a>
            </div>
          </div>
        </nav>

        <!-- Hero Content -->
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title">Your Family Adventure, Planned & Paid Your Way</h1>
            <p class="hero-subtitle">Enter your dream trip details and discover tailored EMI packages instantly</p>
          </div>

          <!-- Search Form -->
          <div class="search-form-container">
            <form class="search-form" id="familySearchForm">
              <div class="form-row">
                <!-- Destination Input -->
                <div class="input-group">
                  <label for="destination" class="input-label">
                    <i class="fas fa-map-marker-alt"></i> Where to?
                  </label>
                  <div class="destination-input-container">
                    <input
                      type="text"
                      id="destination"
                      name="destination"
                      placeholder="e.g., Kashmir, Goa, Manali"
                      class="form-input"
                      autocomplete="off"
                    />
                    <div id="destination-suggestions" class="destination-suggestions"></div>
                  </div>
                  <div class="destination-suggestions" id="destinationSuggestions"></div>
                </div>

                <!-- Travel Date Input -->
                <div class="input-group">
                  <label for="travelDate" class="input-label">
                    <i class="fas fa-calendar-alt"></i> When are you travelling?
                  </label>
                  <input
                    type="date"
                    id="travelDate"
                    name="travelDate"
                    class="form-input"
                    min="2024-12-01"
                    placeholder="Select travel date"
                  />
                </div>

                <!-- Traveler Count Input -->
                <div class="input-group">
                  <label for="travelers" class="input-label">
                    <i class="fas fa-users"></i> Who's going?
                  </label>
                  <button type="button" class="form-input traveler-selector" id="travelerSelector">
                    <span id="travelerDisplay">2 Adults</span>
                    <i class="fas fa-chevron-down"></i>
                  </button>
                </div>
              </div>

              <!-- Search Button -->
              <button type="submit" class="search-btn">
                <i class="fas fa-sparkles"></i> Find My Trip
              </button>
            </form>

            <!-- Auto-detected Family Type Display -->
            <div class="family-type-display" id="familyTypeDisplay">
              <span class="family-type-label">Family Type:</span>
              <span class="family-type-name" id="detectedFamilyType">Stellar Duo (2 Adults)</span>
            </div>

            <!-- Test Button for Demo -->
            <div style="text-align: center; margin-top: 1rem;">
              <button type="button" onclick="showDemoResults()" style="background: var(--green); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 8px; font-weight: 600; cursor: pointer;">
                🎯 Show Demo Results
              </button>
            </div>
          </div>
        </div>

        <!-- Hero Background Elements -->
        <div class="hero-background">
          <img class="hero-plane hero-plane-1" src="img/plane.png" alt="Plane" />
          <img class="hero-plane hero-plane-2" src="img/plane.png" alt="Plane" />
          <div class="hero-decoration"></div>
        </div>
      </section>
        <!-- Dynamic Results Section -->
        <section class="results-section" id="resultsSection" style="display: none;">
          <div class="results-container">
            <!-- Results Header -->
            <div class="results-header">
              <h2 class="results-title" id="resultsTitle">
                Showing packages for <span class="family-type-highlight">Family Nest (2 Adults, 2 Children)</span>
                to <span class="destination-highlight">Kashmir</span> in <span class="date-highlight">October 2025</span>
              </h2>
            </div>

            <!-- Package Cards Grid -->
            <div class="package-grid" id="packageGrid">
              <!-- Package Card 1 -->
              <div class="package-card">
                <div class="package-image">
                  <img src="img/rectangle-14.png" alt="Kashmir Winter Wonderland" />
                  <div class="duration-badge">5N / 6D</div>
                  <div class="offer-badge">
                    <i class="fas fa-gift"></i> 15% OFF
                  </div>
                </div>

                <div class="package-content">
                  <h3 class="package-title">Kashmir Winter Wonderland</h3>

                  <div class="package-inclusions">
                    <div class="inclusion-item">
                      <i class="fas fa-plane"></i>
                      <span>Flights</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-hotel"></i>
                      <span>Hotels</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-utensils"></i>
                      <span>Meals</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-camera"></i>
                      <span>Sightseeing</span>
                    </div>
                  </div>

                  <div class="emi-highlight">
                    <div class="emi-amount">₹4,999<span class="emi-period">/month</span></div>
                    <div class="emi-details">for 8 Prepaid EMIs</div>
                    <div class="total-amount">Total: ₹39,992</div>
                  </div>

                  <button class="view-details-btn" onclick="openPackageModal('kashmir-winter')">
                    View Details
                  </button>
                </div>
              </div>

              <!-- Package Card 2 -->
              <div class="package-card">
                <div class="package-image">
                  <img src="img/rectangle-14-2.png" alt="Goa Beach Paradise" />
                  <div class="duration-badge">4N / 5D</div>
                  <div class="offer-badge early-bird">
                    <i class="fas fa-clock"></i> Early Bird
                  </div>
                </div>

                <div class="package-content">
                  <h3 class="package-title">Goa Beach Paradise</h3>

                  <div class="package-inclusions">
                    <div class="inclusion-item">
                      <i class="fas fa-plane"></i>
                      <span>Flights</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-hotel"></i>
                      <span>Hotels</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-utensils"></i>
                      <span>Meals</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-umbrella-beach"></i>
                      <span>Beach Activities</span>
                    </div>
                  </div>

                  <div class="emi-highlight">
                    <div class="emi-amount">₹3,555<span class="emi-period">/month</span></div>
                    <div class="emi-details">for 9 Prepaid EMIs</div>
                    <div class="total-amount">Total: ₹32,000</div>
                  </div>

                  <button class="view-details-btn" onclick="openPackageModal('goa-beach')">
                    View Details
                  </button>
                </div>
              </div>

              <!-- Package Card 3 -->
              <div class="package-card">
                <div class="package-image">
                  <img src="img/rectangle-14-4.png" alt="Manali Adventure" />
                  <div class="duration-badge">3N / 4D</div>
                  <div class="offer-badge best-value">
                    <i class="fas fa-star"></i> Best Value
                  </div>
                </div>

                <div class="package-content">
                  <h3 class="package-title">Manali Adventure</h3>

                  <div class="package-inclusions">
                    <div class="inclusion-item">
                      <i class="fas fa-plane"></i>
                      <span>Flights</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-hotel"></i>
                      <span>Hotels</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-utensils"></i>
                      <span>Meals</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-mountain"></i>
                      <span>Adventure</span>
                    </div>
                  </div>

                  <div class="emi-highlight">
                    <div class="emi-amount">₹2,799<span class="emi-period">/month</span></div>
                    <div class="emi-details">for 6 Prepaid EMIs</div>
                    <div class="total-amount">Total: ₹16,794</div>
                  </div>

                  <button class="view-details-btn" onclick="openPackageModal('manali-adventure')">
                    View Details
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Features Section -->
        <section class="features-section">
          <div class="features-container">
            <div class="features-header">
              <h2 class="features-title">Why Family EMI Packages</h2>
              <p class="features-subtitle">Make your dream vacation affordable with our monthly prepaid EMI plans</p>
            </div>

            <div class="features-grid">
              <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-exchange-alt"></i>
                </div>
                <h3 class="feature-title">Easy Plan Swap</h3>
                <p class="feature-description">
                  Swap plans 60 days before the travel date, change destinations or upgrade plans.
                </p>
              </div>

              <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-clock"></i>
                </div>
                <h3 class="feature-title">No Last Minute Rush</h3>
                <p class="feature-description">
                  Enjoy your family vacation without the stress of peak season crowds.
                </p>
              </div>

              <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-shield-alt"></i>
                </div>
                <h3 class="feature-title">Guaranteed Availability</h3>
                <p class="feature-description">
                  Secure your booking without worrying about last-minute availability issues.
                </p>
              </div>

              <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-gift"></i>
                </div>
                <h3 class="feature-title">Rewards on Booking</h3>
                <p class="feature-description">
                  Earn benefits on each EMI payment and by referring friends or family.
                </p>
              </div>
            </div>
          </div>
        </section>

        <!-- Traveler Selector Modal -->
        <div class="modal-overlay" id="travelerModal">
          <div class="modal-content traveler-modal">
            <div class="modal-header">
              <h3>Select Travelers</h3>
              <button class="modal-close" onclick="closeTravelerModal()">
                <i class="fas fa-times"></i>
              </button>
            </div>

            <div class="modal-body">
              <div class="traveler-counters">
                <div class="counter-group">
                  <div class="counter-info">
                    <span class="counter-label">Adults</span>
                    <span class="counter-sublabel">(12+ years)</span>
                  </div>
                  <div class="counter-controls">
                    <button type="button" class="counter-btn" onclick="updateCounter('adults', -1)">-</button>
                    <span class="counter-value" id="adultsCount">2</span>
                    <button type="button" class="counter-btn" onclick="updateCounter('adults', 1)">+</button>
                  </div>
                </div>

                <div class="counter-group">
                  <div class="counter-info">
                    <span class="counter-label">Child below 5</span>
                    <span class="counter-sublabel">(2-5 years)</span>
                  </div>
                  <div class="counter-controls">
                    <button type="button" class="counter-btn" onclick="updateCounter('child', -1)">-</button>
                    <span class="counter-value" id="childCount">0</span>
                    <button type="button" class="counter-btn" onclick="updateCounter('child', 1)">+</button>
                  </div>
                </div>

                <div class="counter-group">
                  <div class="counter-info">
                    <span class="counter-label">Children</span>
                    <span class="counter-sublabel">(6-11 years)</span>
                  </div>
                  <div class="counter-controls">
                    <button type="button" class="counter-btn" onclick="updateCounter('children', -1)">-</button>
                    <span class="counter-value" id="childrenCount">0</span>
                    <button type="button" class="counter-btn" onclick="updateCounter('children', 1)">+</button>
                  </div>
                </div>

                <div class="counter-group">
                  <div class="counter-info">
                    <span class="counter-label">Infants</span>
                    <span class="counter-sublabel">(Under 2 years)</span>
                  </div>
                  <div class="counter-controls">
                    <button type="button" class="counter-btn" onclick="updateCounter('infants', -1)">-</button>
                    <span class="counter-value" id="infantsCount">0</span>
                    <button type="button" class="counter-btn" onclick="updateCounter('infants', 1)">+</button>
                  </div>
                </div>
              </div>

              <div class="detected-family-type">
                <span class="family-type-label">Detected Family Type:</span>
                <span class="family-type-value" id="modalFamilyType">Stellar Duo (2 Adults)</span>
              </div>
            </div>

            <div class="modal-footer">
              <button class="apply-btn" onclick="applyTravelerSelection()">
                Apply Selection
              </button>
            </div>
          </div>
        </div>

        <!-- Package Details Modal -->
        <div class="modal-overlay" id="packageModal">
          <div class="modal-content package-modal">
            <div class="modal-header">
              <h3 id="packageModalTitle">Kashmir Winter Wonderland</h3>
              <button class="modal-close" onclick="closePackageModal()">
                <i class="fas fa-times"></i>
              </button>
            </div>

            <div class="modal-body">
              <!-- Image Gallery -->
              <div class="image-gallery">
                <div class="main-image">
                  <img id="packageMainImage" src="img/rectangle-14.png" alt="Package Image" />
                </div>
              </div>

              <!-- Package Tabs -->
              <div class="package-tabs">
                <button class="tab-btn active" onclick="switchTab('overview')">Overview</button>
                <button class="tab-btn" onclick="switchTab('itinerary')">Itinerary</button>
                <button class="tab-btn" onclick="switchTab('emi-options')">EMI Options</button>
                <button class="share-card-btn" onclick="generateAndShareCard(currentPackageId)" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; padding: 0.75rem 1rem; border-radius: 8px; font-weight: 600; cursor: pointer; margin-left: auto;">
                  <i class="fas fa-share-alt"></i> Share Card
                </button>
              </div>

              <!-- Tab Content -->
              <div class="tab-content">
                <!-- Overview Tab -->
                <div class="tab-pane active" id="overview">
                  <div class="package-overview">
                    <div class="overview-item">
                      <strong>Duration:</strong> 5 Nights / 6 Days
                    </div>
                    <div class="overview-item">
                      <strong>Highlights:</strong> Dal Lake, Gulmarg, Pahalgam, Srinagar
                    </div>
                    <div class="overview-item">
                      <strong>Inclusions:</strong>
                      <ul>
                        <li>Round-trip flights</li>
                        <li>4-star hotel accommodation</li>
                        <li>Daily breakfast and dinner</li>
                        <li>All sightseeing and transfers</li>
                        <li>Professional guide</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <!-- Itinerary Tab -->
                <div class="tab-pane" id="itinerary">
                  <div class="itinerary-content">
                    <div class="day-item">
                      <h4>Day 1: Arrival in Srinagar</h4>
                      <p>Arrive at Srinagar airport, transfer to houseboat, Dal Lake shikara ride</p>
                    </div>
                    <div class="day-item">
                      <h4>Day 2: Srinagar to Gulmarg</h4>
                      <p>Drive to Gulmarg, Gondola ride, snow activities</p>
                    </div>
                    <div class="day-item">
                      <h4>Day 3: Gulmarg to Pahalgam</h4>
                      <p>Transfer to Pahalgam, Betaab Valley, Aru Valley visit</p>
                    </div>
                  </div>
                </div>

                <!-- EMI Options Tab -->
                <div class="tab-pane" id="emi-options">
                  <div class="emi-plans">
                    <div class="emi-plan">
                      <div class="plan-header">
                        <h4>3 Months</h4>
                        <span class="plan-label quick-pay">Quick Pay</span>
                      </div>
                      <div class="plan-amount">₹13,997/month</div>
                      <div class="plan-details">
                        <div>Total: ₹41,991</div>
                        <div>Processing Fee: ₹999</div>
                      </div>
                      <button class="select-plan-btn">Select Plan</button>
                    </div>

                    <div class="emi-plan best-value">
                      <div class="plan-header">
                        <h4>6 Months</h4>
                        <span class="plan-label">Best Value</span>
                      </div>
                      <div class="plan-amount">₹6,998/month</div>
                      <div class="plan-details">
                        <div>Total: ₹41,988</div>
                        <div>Processing Fee: ₹996</div>
                      </div>
                      <button class="select-plan-btn">Select Plan</button>
                    </div>

                    <div class="emi-plan">
                      <div class="plan-header">
                        <h4>12 Months</h4>
                        <span class="plan-label low-monthly">Low Monthly</span>
                      </div>
                      <div class="plan-amount">₹3,499/month</div>
                      <div class="plan-details">
                        <div>Total: ₹41,988</div>
                        <div>Processing Fee: ₹996</div>
                      </div>
                      <button class="select-plan-btn">Select Plan</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- JavaScript -->
        <script>
          // Global data storage
          let familyTypesData = [];
          let destinationsData = [];
          let travelers = { adults: 2, child: 0, children: 0, infants: 0 };
          let currentPackages = [];

          // Initialize the page
          document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 Initializing Family EMI Website...');

            try {
              // Load initial data
              await loadInitialData();

              // Setup UI components
              setupDestinationAutocomplete();
              setupFormSubmission();
              updateFamilyTypeDisplay();

              console.log('✅ Website initialized successfully');
            } catch (error) {
              console.error('❌ Error initializing website:', error);
              notificationManager.show('Failed to load initial data. Some features may not work properly.', 'warning');
            }
          });

          // Load initial data from database
          async function loadInitialData() {
            try {
              console.log('🔄 Loading initial data from live database...');

              // Load family types and destinations in parallel using direct database connection
              const [familyTypesResponse, destinationsResponse] = await Promise.all([
                databaseService.getFamilyTypes(),
                databaseService.getDestinations()
              ]);

              if (familyTypesResponse.success) {
                familyTypesData = familyTypesResponse.data;
                console.log('📊 Loaded family types from CRM DB:', familyTypesData.length);
              }

              if (destinationsResponse.success) {
                destinationsData = destinationsResponse.data;
                console.log('🗺️ Loaded destinations from Quote DB:', destinationsData.length);

                // Initialize destination autocomplete
                initializeDestinationAutocomplete(destinationsData);
              }

              // Show success notification
              if (familyTypesData.length > 0 && destinationsData.length > 0) {
                notificationManager.show(`✅ Loaded ${familyTypesData.length} family types and ${destinationsData.length} destinations`, 'success', 3000);
              }

            } catch (error) {
              console.error('❌ Error loading initial data:', error);
              notificationManager.show('⚠️ Using offline data. Some features may be limited.', 'warning');

              // Fallback to static data
              familyTypesData = [
                { family_id: 'SD', family_type: 'Stellar Duo', composition: '2 Adults' },
                { family_id: 'BB', family_type: 'Baby Bliss', composition: '2 Adults + 1 Infant' },
                { family_id: 'TD', family_type: 'Tiny Delight', composition: '2 Adults + 1 Child' }
              ];
              destinationsData = [
                { destination: 'Kashmir' }, { destination: 'Goa' }, { destination: 'Manali' }
              ];
            }
          }

          // Setup destination autocomplete
          function setupDestinationAutocomplete() {
            const destinationInput = document.getElementById('destination');
            const suggestionsDiv = document.getElementById('destinationSuggestions');

            destinationInput.addEventListener('input', function() {
              const value = this.value.toLowerCase();
              const filtered = destinationsData.filter(dest =>
                dest.destination.toLowerCase().includes(value)
              );

              if (value && filtered.length > 0) {
                suggestionsDiv.innerHTML = filtered.map(dest =>
                  `<div class="suggestion-item" onclick="selectDestination('${dest.destination}')">${dest.destination}</div>`
                ).join('');
                suggestionsDiv.style.display = 'block';
              } else {
                suggestionsDiv.style.display = 'none';
              }
            });
          }

          // Select destination from suggestions
          function selectDestination(destination) {
            document.getElementById('destination').value = destination;
            document.getElementById('destinationSuggestions').style.display = 'none';
          }

          // Hide suggestions when clicking outside
          document.addEventListener('click', function(event) {
            if (!event.target.closest('.input-group')) {
              document.getElementById('destinationSuggestions').style.display = 'none';
            }
          });

          // Setup form submission
          function setupFormSubmission() {
            document.getElementById('familySearchForm').addEventListener('submit', function(e) {
              e.preventDefault();
              searchPackages();
            });

            document.getElementById('travelerSelector').addEventListener('click', function() {
              openTravelerModal();
            });
          }

          // Search packages
          async function searchPackages() {
            const destination = document.getElementById('destination').value;
            const travelDate = document.getElementById('travelDate').value;

            if (!destination || !travelDate) {
              notificationManager.show('Please fill in all fields', 'warning');
              return;
            }

            try {
              // Prepare search parameters
              const searchParams = {
                destination,
                travel_date: travelDate,
                adults: travelers.adults,
                child: travelers.child,
                children: travelers.children,
                infants: travelers.infants
              };

              console.log('🔍 Searching packages with params:', searchParams);

              // Call database service to search packages directly
              const response = await databaseService.searchPackages(searchParams);

              if (response.success) {
                currentPackages = response.packages;

                if (response.packages && response.packages.length > 0) {
                  displaySearchResults(response);
                  notificationManager.show(`Found ${response.packages.length} packages for ${destination}`, 'success');
                } else {
                  displayNoPackagesMessage(destination, response.matched_family_type);
                }

                // Show results section
                const resultsSection = document.getElementById('resultsSection');
                resultsSection.style.display = 'block';
                resultsSection.classList.add('show');
                resultsSection.scrollIntoView({ behavior: 'smooth' });
              } else {
                displayNoPackagesMessage(destination);
                notificationManager.show('Error searching packages: ' + response.error, 'error');
              }
            } catch (error) {
              console.error('Error searching packages:', error);
              displayNoPackagesMessage(destination);
              notificationManager.show(error.message || 'Failed to search packages. Please try again.', 'error');
            }
          }

          // Display no packages available message
          function displayNoPackagesMessage(destination, familyType) {
            const resultsSection = document.getElementById('resultsSection');
            const packageGrid = document.getElementById('packageGrid');
            const resultsTitle = document.getElementById('resultsTitle');

            // Update title
            const familyTypeText = familyType ? familyType.family_type : 'your family';
            resultsTitle.innerHTML = `
              No packages available for <span class="family-type-highlight">${familyTypeText}</span>
              to <span class="destination-highlight">${destination}</span>
            `;

            // Display no packages message
            packageGrid.innerHTML = `
              <div class="no-packages-message">
                <div class="no-packages-icon">
                  <i class="fas fa-search"></i>
                </div>
                <h3>No packages available right now</h3>
                <p>We're working on adding more packages for ${destination}. We'll update you soon!</p>
                <div class="no-packages-actions">
                  <button class="notify-btn" onclick="requestNotification('${destination}')">
                    <i class="fas fa-bell"></i> Notify me when available
                  </button>
                  <button class="explore-btn" onclick="exploreOtherDestinations()">
                    <i class="fas fa-map"></i> Explore other destinations
                  </button>
                </div>
              </div>
            `;

            // Show results section
            resultsSection.style.display = 'block';
            resultsSection.classList.add('show');
            resultsSection.scrollIntoView({ behavior: 'smooth' });
          }

          // Request notification for destination
          function requestNotification(destination) {
            // You can implement this to collect email for notifications
            notificationManager.show(`We'll notify you when packages for ${destination} are available!`, 'success');
          }

          // Explore other destinations
          function exploreOtherDestinations() {
            // Clear destination input and focus
            document.getElementById('destination').value = '';
            document.getElementById('destination').focus();

            // Hide results section
            document.getElementById('resultsSection').style.display = 'none';
          }

          // Display search results
          function displaySearchResults(response) {
            const { matched_family_type, packages, search_params } = response;

            // Update results header
            const dateObj = new Date(search_params.travel_date + '-01');
            const monthYear = dateObj.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

            document.getElementById('resultsTitle').innerHTML = `
              Showing packages for <span class="family-type-highlight">${matched_family_type.family_type}</span>
              to <span class="destination-highlight">${search_params.destination}</span> in <span class="date-highlight">${monthYear}</span>
            `;

            // Update package grid
            const packageGrid = document.getElementById('packageGrid');
            if (packages.length === 0) {
              packageGrid.innerHTML = `
                <div class="no-packages">
                  <h3>No packages found</h3>
                  <p>Try searching for a different destination or travel date.</p>
                </div>
              `;
              return;
            }

            packageGrid.innerHTML = packages.map(pkg => createPackageCard(pkg)).join('');
          }

          // Create package card HTML
          function createPackageCard(pkg) {
            // Ensure EMI options exist and have valid data
            const emiOptions = pkg.emi_options || [];
            const bestEMI = emiOptions.find(emi => emi.is_featured) || emiOptions[0] || {
              monthly_amount: Math.round((pkg.total_price || 45000) / 6),
              months: 6,
              total_amount: pkg.total_price || 45000
            };

            // Ensure all required properties exist
            const safePackage = {
              id: pkg.id || 'unknown',
              title: pkg.title || 'Travel Package',
              duration_days: pkg.duration_days || 5,
              offer_badge: pkg.offer_badge || 'Special Offer',
              images: pkg.images || ['img/rectangle-14.png'],
              inclusions: pkg.inclusions || ['Flights', 'Hotels', 'Meals']
            };

            return `
              <div class="package-card">
                <div class="package-image">
                  <img src="${safePackage.images[0]}" alt="${safePackage.title}" />
                  <div class="duration-badge">${safePackage.duration_days}</div>
                  <div class="offer-badge">
                    <i class="fas fa-gift"></i> ${safePackage.offer_badge}
                  </div>
                </div>

                <div class="package-content">
                  <h3 class="package-title">${pkg.quote_name || pkg.package_name || pkg.title}</h3>

                  <div class="package-inclusions">
                    ${safePackage.inclusions.map(inc => `
                      <div class="inclusion-item">
                        <i class="fas fa-${getInclusionIcon(inc)}"></i>
                        <span>${toTitleCase(inc)}</span>
                      </div>
                    `).join('')}
                  </div>

                  <div class="emi-highlight">
                    <div class="emi-amount">₹${(bestEMI.monthly_amount || 0).toLocaleString()}<span class="emi-period">/month</span></div>
                    <div class="emi-details">for ${bestEMI.months || 6} Prepaid EMIs</div>
                    <div class="total-amount">Total: ₹${(bestEMI.total_amount || 0).toLocaleString()}</div>
                  </div>

                  <button class="view-details-btn" onclick="openPackageModal('${safePackage.id}')">
                    View Details
                  </button>
                </div>
              </div>
            `;
          }

          // Get icon for inclusion type
          function getInclusionIcon(inclusion) {
            const iconMap = {
              'Flights': 'plane',
              'Hotels': 'hotel',
              'Meals': 'utensils',
              'Sightseeing': 'camera',
              'Beach Activities': 'umbrella-beach',
              'Adventure': 'mountain'
            };
            return iconMap[inclusion] || 'check';
          }

          // Helper function to convert text to title case (excluding hotel names)
          function toTitleCase(str) {
            if (!str || typeof str !== 'string') return str;

            // Don't convert hotel names (they contain specific patterns)
            if (str.includes('Hotel') || str.includes('Resort') || str.includes('(') || str.includes('N -') || str.includes('N Hotel')) {
              return str;
            }

            return str.toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
          }

          // Helper function to convert filename parts to title case
          function toTitleCaseFilename(str) {
            if (!str || typeof str !== 'string') return str;
            return str.toLowerCase().replace(/\b\w/g, l => l.toUpperCase()).replace(/[^a-z0-9]/gi, '_');
          }

          // Extract only the family type name from database format
          function extractFamilyTypeName(fullFamilyType) {
            if (!fullFamilyType) return 'Family Package';

            // Database format: "Stellar Duo - 2 Adults + 1 Child (2 to 5 yrs) + 1 Children (5 to 11 yrs)"
            // We want only: "Stellar Duo"
            const parts = fullFamilyType.split(' - ');
            return parts[0].trim();
          }

          // Detect family type based on traveler counts
          function detectFamilyType() {
            const { adults, child, children, infants } = travelers;

            if (familyTypesData.length === 0) {
              return {
                family_type: 'Custom Family',
                composition: `${adults} Adults${child > 0 ? ` + ${child} Child (2-5 yrs)` : ''}${children > 0 ? ` + ${children} Children (6-11 yrs)` : ''}${infants > 0 ? ` + ${infants} Infants` : ''}`
              };
            }

            // Find exact match first
            let match = familyTypesData.find(ft =>
              ft.no_of_adults === adults &&
              ft.no_of_child === child &&
              ft.no_of_children === children &&
              ft.no_of_infants === infants
            );

            // If no exact match, find closest match
            if (!match) {
              match = familyTypesData.find(ft =>
                ft.no_of_adults === adults &&
                ft.no_of_child >= child &&
                ft.no_of_children >= children &&
                ft.no_of_infants >= infants
              );
            }

            // Default to first family type if no match
            if (!match) {
              match = familyTypesData[0] || {
                family_type: 'Custom Family',
                composition: `${adults} Adults${child > 0 ? ` + ${child} Child (2-5 yrs)` : ''}${children > 0 ? ` + ${children} Children (6-11 yrs)` : ''}${infants > 0 ? ` + ${infants} Infants` : ''}`
              };
            }

            return match;
          }

          // Update family type display
          function updateFamilyTypeDisplay() {
            const familyType = detectFamilyType();
            document.getElementById('detectedFamilyType').textContent = familyType.family_type;

            // Update traveler display with count
            const { adults, child, children, infants } = travelers;
            let displayText = `${adults} Adult${adults > 1 ? 's' : ''}`;
            if (child > 0) displayText += `, ${child} Child (2-5 yrs)`;
            if (children > 0) displayText += `, ${children} Children (6-11 yrs)`;
            if (infants > 0) displayText += `, ${infants} Infant${infants > 1 ? 's' : ''}`;

            document.getElementById('travelerDisplay').textContent = displayText;

            if (document.getElementById('modalFamilyType')) {
              document.getElementById('modalFamilyType').textContent = familyType.family_type;
            }
          }

          // Traveler Modal Functions
          function openTravelerModal() {
            document.getElementById('travelerModal').style.display = 'flex';
          }

          function closeTravelerModal() {
            document.getElementById('travelerModal').style.display = 'none';
          }

          function updateCounter(type, change) {
            travelers[type] = Math.max(0, travelers[type] + change);
            if (type === 'adults') travelers[type] = Math.max(1, travelers[type]); // At least 1 adult

            document.getElementById(type + 'Count').textContent = travelers[type];
            updateFamilyTypeDisplay();
          }

          function applyTravelerSelection() {
            updateFamilyTypeDisplay();
            closeTravelerModal();
          }

          // Package Modal Functions
          async function openPackageModal(packageId) {
            try {
              // Set current package ID for card generation
              currentPackageId = packageId;

              const modal = document.getElementById('packageModal');
              modal.style.display = 'flex';

              // Show loading state
              document.getElementById('packageModalTitle').textContent = 'Loading...';

              // Load package details from database
              const response = await databaseService.getPackageDetails(packageId);

              if (response.success) {
                populatePackageModal(response.package);
              } else {
                notificationManager.show('Failed to load package details', 'error');
                closePackageModal();
              }
            } catch (error) {
              console.error('Error loading package details:', error);
              notificationManager.show('Failed to load package details', 'error');
              closePackageModal();
            }
          }

          // Populate package modal with data
          function populatePackageModal(pkg) {
            // Update title and image
            document.getElementById('packageModalTitle').textContent = pkg.package_name || pkg.title || 'Travel Package';
            document.getElementById('packageMainImage').src = pkg.images?.[0] || 'img/rectangle-14.png';

            // Update overview tab with enhanced package details
            const overviewTab = document.getElementById('overview');
            overviewTab.innerHTML = `
              <div class="package-overview">
                <!-- Package Summary Card -->
                <div class="package-summary-card">
                  <div class="summary-header">
                    <h4>${pkg.package_name || pkg.title || 'Travel Package'}</h4>
                    <div class="package-badges">
                      ${pkg.offer_badge ? `<span class="offer-badge">${pkg.offer_badge}</span>` : ''}
                      ${pkg.category ? `<span class="category-badge">${pkg.category}</span>` : ''}
                    </div>
                  </div>

                  <div class="summary-details">
                    <div class="detail-row">
                      <div class="detail-item">
                        <i class="fas fa-calendar-alt"></i>
                        <span><strong>Duration:</strong> ${pkg.nights || pkg.duration_days}</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span><strong>Destination:</strong> ${pkg.destination || 'Travel Destination'}</span>
                      </div>
                    </div>

                    <div class="detail-row">
                      <div class="detail-item" style="grid-column: 1 / -1;">
                        <i class="fas fa-hotel"></i>
                        <span><strong>Hotels:</strong></span>
                        <div style="margin-top: 8px;">
                          ${pkg.hotels_list && pkg.hotels_list.length > 0 ?
                            pkg.hotels_list.map(hotel => {
                              const nights = hotel.nights || hotel.stay_nights || 1;
                              const hotelName = hotel.hotel_name || 'Hotel Included';
                              const mealPlan = hotel.meal_plan || 'Breakfast included';
                              return `<div style="padding: 4px 0;  border-left: 3px solid #667eea; padding-left: 8px; margin: 4px 0;">${nights}N - ${hotelName} (${mealPlan})</div>`;
                            }).join('') :
                            `<div>${pkg.hotel_name || 'Hotel Included'} (${pkg.hotel_category || 'Standard'})</div>`
                          }
                        </div>
                      </div>
                    </div>

                    <div class="detail-row">
                      <div class="detail-item">
                        <i class="fas fa-users"></i>
                        <span><strong>Family Type:</strong> ${pkg.family_type || 'Family Package'}</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-rupee-sign"></i>
                        <span><strong>Total Price:</strong> ₹${(pkg.total_price || 0).toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Package Description -->
                ${pkg.description ? `
                <div class="package-description">
                  <h5><i class="fas fa-info-circle"></i> About This Package</h5>
                  <p>${pkg.description}</p>
                </div>
                ` : ''}

                <!-- Inclusions Section -->
                <div class="package-inclusions">
                  <h5><i class="fas fa-check-circle text-success"></i> What's Included</h5>
                  <div class="inclusions-grid">
                    ${(pkg.inclusions || ['Accommodation', 'Transfers']).map(inc => `
                      <div class="inclusion-item">
                        <i class="fas fa-check text-success"></i>
                        <span>${toTitleCase(inc)}</span>
                      </div>
                    `).join('')}
                  </div>
                </div>

                <!-- Exclusions Section -->
                <div class="package-exclusions">
                  <h5><i class="fas fa-times-circle text-danger"></i> What's Not Included</h5>
                  <div class="exclusions-grid">
                    ${(pkg.exclusions || ['Personal expenses', 'Travel insurance', 'Items not mentioned in inclusions']).map(exc => `
                      <div class="exclusion-item">
                        <i class="fas fa-times text-danger"></i>
                        <span>${exc}</span>
                      </div>
                    `).join('')}
                  </div>
                </div>

                <!-- Cost Breakdown (if available) -->
                ${pkg.cost_breakdown && pkg.cost_breakdown.length > 0 ? `
                <div class="cost-breakdown">
                  <h5><i class="fas fa-calculator"></i> Cost Breakdown</h5>
                  <div class="breakdown-table">
                    ${pkg.cost_breakdown.map(item => `
                      <div class="breakdown-row">
                        <div class="breakdown-item">${item.item}</div>
                        <div class="breakdown-description">${item.description}</div>
                        <div class="breakdown-cost">₹${item.cost.toLocaleString()}</div>
                      </div>
                    `).join('')}
                  </div>
                </div>
                ` : ''}

                <!-- Additional Information -->
                <div class="additional-info">
                  <div class="info-grid">
                    ${pkg.ferry_included ? `
                    <div class="info-item">
                      <i class="fas fa-ship text-primary"></i>
                      <span>Ferry Included</span>
                    </div>
                    ` : ''}
                    ${pkg.guide_included ? `
                    <div class="info-item">
                      <i class="fas fa-user-tie text-primary"></i>
                      <span>Professional Guide</span>
                    </div>
                    ` : ''}
                    ${pkg.activities_included && pkg.activities_included.length > 0 ? `
                    <div class="info-item">
                      <i class="fas fa-hiking text-primary"></i>
                      <span>Activities Included</span>
                    </div>
                    ` : ''}
                  </div>
                </div>

                <!-- Package Validity -->
                ${pkg.validity ? `
                <div class="package-validity">
                  <i class="fas fa-calendar-check"></i>
                  <span>${pkg.validity}</span>
                </div>
                ` : ''}
              </div>
            `;

            // Update itinerary tab with enhanced layout
            const itineraryTab = document.getElementById('itinerary');
            if (pkg.itinerary && pkg.itinerary.length > 0) {
              itineraryTab.innerHTML = `
                <div class="itinerary-content">
                  <div class="itinerary-header">
                    <h4><i class="fas fa-route"></i> Detailed Itinerary</h4>
                    <p>Day-wise breakdown of your ${pkg.nights || pkg.duration_days || 5}-night journey to ${pkg.destination}</p>
                  </div>

                  <div class="itinerary-timeline">
                    ${pkg.itinerary.map((day, index) => `
                      <div class="day-item ${index === 0 ? 'first-day' : ''} ${index === pkg.itinerary.length - 1 ? 'last-day' : ''}">
                        <div class="day-marker">
                          <div class="day-number">${day.day}</div>
                          <div class="day-line"></div>
                        </div>
                        <div class="day-content">
                          <div class="day-header">
                            <h5>Day ${day.day}: ${day.title}</h5>
                            ${index === 0 ? '<span class="day-badge arrival">Arrival</span>' : ''}
                            ${index === pkg.itinerary.length - 1 ? '<span class="day-badge departure">Departure</span>' : ''}
                          </div>
                          <p class="day-description">${day.description}</p>
                          ${day.highlights ? `
                          <div class="day-highlights">
                            <strong>Highlights:</strong>
                            <ul>
                              ${day.highlights.map(highlight => `<li>${highlight}</li>`).join('')}
                            </ul>
                          </div>
                          ` : ''}
                        </div>
                      </div>
                    `).join('')}
                  </div>

                  <!-- Activities Section -->
                  ${pkg.activities_included && pkg.activities_included.length > 0 ? `
                  <div class="included-activities">
                    <h5><i class="fas fa-star"></i> Special Activities Included</h5>
                    <div class="activities-grid">
                      ${pkg.activities_included.map(activity => `
                        <div class="activity-item">
                          <i class="fas fa-check-circle"></i>
                          <span>${activity}</span>
                        </div>
                      `).join('')}
                    </div>
                  </div>
                  ` : ''}

                  <!-- Important Notes -->
                  <div class="itinerary-notes">
                    <h6><i class="fas fa-info-circle"></i> Important Notes</h6>
                    <ul>
                      <li>Itinerary is subject to change based on weather conditions and local circumstances</li>
                      <li>Check-in time is usually 2:00 PM and check-out time is 12:00 PM</li>
                      <li>All timings are approximate and may vary based on traffic and other factors</li>
                      ${pkg.ferry_included ? '<li>Ferry timings are subject to weather conditions</li>' : ''}
                    </ul>
                  </div>
                </div>
              `;
            } else {
              // Generate basic itinerary based on duration
              const days = pkg.nights || pkg.duration_days || 5;
              const destination = pkg.destination || 'your destination';
              itineraryTab.innerHTML = `
                <div class="itinerary-content">
                  <div class="itinerary-header">
                    <h4><i class="fas fa-route"></i> Itinerary Overview</h4>
                    <p>Your ${days}-night journey to ${destination}</p>
                  </div>

                  <div class="itinerary-timeline">
                    <div class="day-item first-day">
                      <div class="day-marker">
                        <div class="day-number">1</div>
                        <div class="day-line"></div>
                      </div>
                      <div class="day-content">
                        <div class="day-header">
                          <h5>Day 1: Arrival in ${destination}</h5>
                          <span class="day-badge arrival">Arrival</span>
                        </div>
                        <p class="day-description">Arrive at ${destination}, meet and greet by our representative, transfer to hotel and check-in. Rest of the day at leisure.</p>
                      </div>
                    </div>

                    ${Array.from({length: days - 1}, (_, i) => `
                      <div class="day-item">
                        <div class="day-marker">
                          <div class="day-number">${i + 2}</div>
                          <div class="day-line"></div>
                        </div>
                        <div class="day-content">
                          <div class="day-header">
                            <h5>Day ${i + 2}: ${destination} Sightseeing</h5>
                          </div>
                          <p class="day-description">Explore the beautiful attractions and local culture of ${destination}. Visit major tourist spots and enjoy local experiences.</p>
                        </div>
                      </div>
                    `).join('')}

                    <div class="day-item last-day">
                      <div class="day-marker">
                        <div class="day-number">${days + 1}</div>
                        <div class="day-line"></div>
                      </div>
                      <div class="day-content">
                        <div class="day-header">
                          <h5>Day ${days + 1}: Departure</h5>
                          <span class="day-badge departure">Departure</span>
                        </div>
                        <p class="day-description">After breakfast, check-out from hotel and transfer to airport/railway station for onward journey.</p>
                      </div>
                    </div>
                  </div>

                  <div class="itinerary-notes">
                    <h6><i class="fas fa-info-circle"></i> Important Notes</h6>
                    <ul>
                      <li>Detailed itinerary will be provided upon booking confirmation</li>
                      <li>Itinerary is subject to change based on weather conditions</li>
                      <li>All timings are approximate and may vary</li>
                    </ul>
                  </div>
                </div>
              `;
            }

            // Update EMI options tab
            const emiTab = document.getElementById('emi-options');
            const emiOptions = pkg.emi_options || [];

            if (emiOptions.length === 0) {
              emiTab.innerHTML = `
                <div class="emi-plans">
                  <div class="no-emi-plans">
                    <h3>EMI Options Coming Soon</h3>
                    <p>Please contact us for flexible payment options.</p>
                    <button class="select-plan-btn" onclick="selectEMIPlan('${pkg.id}', 'contact')">
                      Contact for EMI
                    </button>
                  </div>
                </div>
              `;
            } else {
              emiTab.innerHTML = `
                <div class="emi-plans">
                  ${emiOptions.map(emi => `
                    <div class="emi-plan ${emi.is_featured ? 'best-value' : ''}">
                      <div class="plan-header">
                        <h4>${emi.months || 6} Months</h4>
                        <span class="plan-label ${(emi.label || 'standard').toLowerCase().replace(' ', '-')}">${emi.label || 'Standard'}</span>
                      </div>
                      <div class="plan-amount">₹${(emi.monthly_amount || 0).toLocaleString()}/month</div>
                      <div class="plan-details">
                        <div>Total: ₹${(emi.total_amount || 0).toLocaleString()}</div>
                        <div>Processing Fee: ₹${(emi.processing_fee || 0).toLocaleString()}</div>
                      </div>
                      <button class="select-plan-btn" onclick="selectEMIPlan('${pkg.id}', '${emi.id || 'default'}')">
                        Select Plan
                      </button>
                    </div>
                  `).join('')}
                </div>
              `;
            }
          }

          // Select EMI plan and proceed to booking
          function selectEMIPlan(packageId, emiPlanId) {
            console.log('Selected EMI plan:', { packageId, emiPlanId });

            // Show quote request form
            showQuoteRequestForm(packageId, emiPlanId);
          }

          // Show quote request form
          function showQuoteRequestForm(packageId, emiPlanId) {
            const formHTML = `
              <div class="quote-form">
                <h3>Request Quote</h3>
                <form id="quoteRequestForm">
                  <div class="form-group">
                    <label for="customerName">Full Name *</label>
                    <input type="text" id="customerName" name="customerName" required>
                  </div>
                  <div class="form-group">
                    <label for="customerEmail">Email *</label>
                    <input type="email" id="customerEmail" name="customerEmail" required>
                  </div>
                  <div class="form-group">
                    <label for="customerPhone">Phone Number *</label>
                    <input type="tel" id="customerPhone" name="customerPhone" required>
                  </div>
                  <div class="form-actions">
                    <button type="button" onclick="closePackageModal()">Cancel</button>
                    <button type="submit">Submit Quote Request</button>
                  </div>
                </form>
              </div>
            `;

            // Replace EMI options tab with quote form
            document.getElementById('emi-options').innerHTML = formHTML;

            // Switch to EMI options tab
            switchTab('emi-options');

            // Setup form submission
            document.getElementById('quoteRequestForm').addEventListener('submit', async (e) => {
              e.preventDefault();
              await submitQuoteRequest(packageId, emiPlanId);
            });
          }

          // Submit quote request
          async function submitQuoteRequest(packageId, emiPlanId) {
            try {
              const formData = new FormData(document.getElementById('quoteRequestForm'));

              const quoteData = {
                customer_name: formData.get('customerName'),
                customer_email: formData.get('customerEmail'),
                customer_phone: formData.get('customerPhone'),
                destination: document.getElementById('destination').value,
                travel_date: document.getElementById('travelDate').value,
                adults: travelers.adults,
                child: travelers.child,
                children: travelers.children,
                infants: travelers.infants,
                selected_package_id: packageId,
                selected_emi_plan_id: emiPlanId
              };

              const response = await databaseService.submitQuoteRequest(quoteData);

              if (response.success) {
                closePackageModal();
                notificationManager.show('Quote request submitted successfully! Our team will contact you soon.', 'success');
              }
            } catch (error) {
              console.error('Error submitting quote request:', error);
              // Error is already handled by the API service
            }
          }

          function closePackageModal() {
            document.getElementById('packageModal').style.display = 'none';
          }

          function switchTab(tabName) {
            // Remove active class from all tabs and panes
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

            // Add active class to clicked tab and corresponding pane
            event.target.classList.add('active');
            document.getElementById(tabName).classList.add('active');
          }

          // Close modals when clicking outside
          window.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal-overlay')) {
              event.target.style.display = 'none';
            }
          });

          // Demo function to show results
          function showDemoResults() {
            document.getElementById('resultsTitle').innerHTML = `
              Showing packages for <span class="family-type-highlight">Stellar Duo (2 Adults)</span>
              to <span class="destination-highlight">Kashmir</span> in <span class="date-highlight">December 2024</span>
            `;

            const resultsSection = document.getElementById('resultsSection');
            resultsSection.style.display = 'block';
            resultsSection.classList.add('show');
            resultsSection.scrollIntoView({ behavior: 'smooth' });
          }

          // Initialize destination autocomplete
          function initializeDestinationAutocomplete(destinations) {
            const destinationInput = document.getElementById('destination');
            const suggestionsContainer = document.getElementById('destination-suggestions');
            let currentHighlight = -1;

            if (!destinationInput || !suggestionsContainer || !destinations) return;

            destinationInput.addEventListener('input', function() {
              const query = this.value.trim().toLowerCase();

              if (query.length < 2) {
                hideSuggestions();
                return;
              }

              // Filter destinations based on query
              const filteredDestinations = destinations.filter(dest =>
                dest.destination.toLowerCase().includes(query) ||
                dest.category.toLowerCase().includes(query)
              ).slice(0, 8); // Limit to 8 suggestions

              showSuggestions(filteredDestinations);
            });

            destinationInput.addEventListener('keydown', function(e) {
              const suggestions = suggestionsContainer.querySelectorAll('.suggestion-item');

              if (e.key === 'ArrowDown') {
                e.preventDefault();
                currentHighlight = Math.min(currentHighlight + 1, suggestions.length - 1);
                updateHighlight(suggestions);
              } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                currentHighlight = Math.max(currentHighlight - 1, -1);
                updateHighlight(suggestions);
              } else if (e.key === 'Enter') {
                e.preventDefault();
                if (currentHighlight >= 0 && suggestions[currentHighlight]) {
                  selectSuggestion(suggestions[currentHighlight]);
                }
              } else if (e.key === 'Escape') {
                hideSuggestions();
              }
            });

            // Hide suggestions when clicking outside
            document.addEventListener('click', function(e) {
              if (!destinationInput.contains(e.target) && !suggestionsContainer.contains(e.target)) {
                hideSuggestions();
              }
            });

            function showSuggestions(destinations) {
              if (destinations.length === 0) {
                hideSuggestions();
                return;
              }

              suggestionsContainer.innerHTML = destinations.map(dest => `
                <div class="suggestion-item" data-destination="${dest.destination}">
                  <div>
                    <div class="suggestion-name">${dest.destination}</div>
                    <div class="suggestion-category">${dest.category}</div>
                  </div>
                  ${dest.packages_available ? `<div class="suggestion-packages">${dest.packages_available} packages</div>` : ''}
                </div>
              `).join('');

              // Add click handlers
              suggestionsContainer.querySelectorAll('.suggestion-item').forEach(item => {
                item.addEventListener('click', () => selectSuggestion(item));
              });

              suggestionsContainer.classList.add('show');
              currentHighlight = -1;
            }

            function hideSuggestions() {
              suggestionsContainer.classList.remove('show');
              currentHighlight = -1;
            }

            function updateHighlight(suggestions) {
              suggestions.forEach((item, index) => {
                item.classList.toggle('highlighted', index === currentHighlight);
              });
            }

            function selectSuggestion(item) {
              const destination = item.dataset.destination;
              destinationInput.value = destination;
              hideSuggestions();
            }
          }

          // Package Card Generation Functions
          let currentPackageId = null;
          let packageCardGenerator = null;

          // Initialize package card generator
          function initPackageCardGenerator() {
            if (!packageCardGenerator) {
              packageCardGenerator = new PackageCardGenerator();
            }
          }

          // Generate and share package card
          async function generateAndShareCard(packageId) {
            try {
              initPackageCardGenerator();

              // Find the package data
              const pkg = currentPackages.find(p => p.id === packageId);
              if (!pkg) {
                notificationManager.show('Package not found', 'error');
                return;
              }

              // Show loading notification
              notificationManager.show('Generating package card...', 'info');

              // Get selected EMI option (check for currently selected EMI)
              const selectedEmiOption = pkg.emi_options && pkg.emi_options.length > 0 ?
                pkg.emi_options.find(emi => emi.selected) || pkg.emi_options[0] : null;

              // Prepare package data for card generation
              const cardData = {
                destination: pkg.destination || 'Destination',
                duration: `${pkg.duration_days || 5}N/${(pkg.duration_days || 5) + 1}D`,
                image: pkg.images && pkg.images[0] ? pkg.images[0] : './img/rectangle-14.png',
                travelMode: 'TRAIN (3AC)', // Default, can be customized
                nights: pkg.duration_days || 5,
                mealPlan: pkg.hotels_list && pkg.hotels_list[0] ?
                  pkg.hotels_list[0].meal_plan : 'Breakfast + Dinner',
                family: {
                  name: pkg.family_type || 'FAMILY NEST',
                  composition: formatFamilyComposition(travelers)
                },
                emi: {
                  months: selectedEmiOption ? selectedEmiOption.months : 10
                },
                price: selectedEmiOption ? selectedEmiOption.monthly_amount : Math.round(pkg.total_price / 10)
              };

              // Generate the card
              const canvas = await packageCardGenerator.generatePackageCard(cardData);

              // Show preview modal
              showCardPreview(canvas, cardData);

            } catch (error) {
              console.error('Error generating package card:', error);
              notificationManager.show('Error generating package card', 'error');
            }
          }

          // Show card preview modal
          function showCardPreview(canvas, cardData) {
            const previewHTML = `
              <div class="card-preview-modal">
                <div class="card-preview-content">
                  <div class="card-preview-header">
                    <h3>Package Card Preview</h3>
                    <button class="modal-close" onclick="closeCardPreview()">&times;</button>
                  </div>
                  <div class="card-preview-body">
                    <div class="card-preview-image">
                      <canvas id="previewCanvas"></canvas>
                    </div>
                    <div class="card-preview-actions">
                      <button class="download-btn" onclick="downloadPackageCard()">
                        <i class="fas fa-download"></i> Download Card
                      </button>
                      <button class="share-btn" onclick="sharePackageCard()">
                        <i class="fas fa-share-alt"></i> Share Card
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            `;

            // Add modal to page
            document.body.insertAdjacentHTML('beforeend', previewHTML);

            // Copy canvas to preview
            const previewCanvas = document.getElementById('previewCanvas');
            const previewCtx = previewCanvas.getContext('2d');
            previewCanvas.width = canvas.width;
            previewCanvas.height = canvas.height;
            previewCanvas.style.width = '300px';
            previewCanvas.style.height = 'auto';
            previewCtx.drawImage(canvas, 0, 0);

            // Store canvas for download/share
            window.currentCardCanvas = canvas;
            window.currentCardData = cardData;
          }

          // Close card preview
          function closeCardPreview() {
            const modal = document.querySelector('.card-preview-modal');
            if (modal) {
              modal.remove();
            }
          }

          // Download package card
          function downloadPackageCard() {
            if (window.currentCardCanvas && window.currentCardData) {
              const familyType = detectFamilyType();
              const familyTypeName = extractFamilyTypeName(familyType.family_type) || 'Family';
              const emiMonths = window.currentCardData.emi?.months || 6;
              packageCardGenerator.downloadCard(
                window.currentCardCanvas,
                `${toTitleCaseFilename(window.currentCardData.destination)}_${toTitleCaseFilename(familyTypeName)}_${emiMonths}Emi_Package_Card.png`
              );
              notificationManager.show('Package card downloaded!', 'success');
            }
          }

          // Share package card
          async function sharePackageCard() {
            if (window.currentCardCanvas && window.currentCardData) {
              await packageCardGenerator.shareCard(
                window.currentCardCanvas,
                window.currentCardData
              );
            }
          }

          // Format family composition for card without duplicates
          function formatFamilyComposition(travelers) {
            let parts = [];

            if (travelers.adults > 0) {
              parts.push(`${travelers.adults} ADULT${travelers.adults > 1 ? 'S' : ''}`);
            }

            if (travelers.child > 0) {
              parts.push(`${travelers.child} CHILD${travelers.child > 1 ? 'REN' : ''} (2-5 YRS)`);
            }

            if (travelers.children > 0) {
              parts.push(`${travelers.children} CHILD${travelers.children > 1 ? 'REN' : ''} (6-11 YRS)`);
            }

            if (travelers.teenagers > 0) {
              parts.push(`${travelers.teenagers} TEENAGER${travelers.teenagers > 1 ? 'S' : ''} (ABOVE 11 YRS)`);
            }

            if (travelers.infants > 0) {
              parts.push(`${travelers.infants} INFANT${travelers.infants > 1 ? 'S' : ''} (BELOW 2 YRS)`);
            }

            return parts.join(' + ');
          }


        </script>

        <!-- Footer -->
        <footer class="footer-section">
          <div class="footer-container">
            <div class="footer-content">
              <div class="footer-brand">
                <div class="footer-logo">
                  <span class="logo-trip">TRIP</span><span class="logo-xplo">XPLO</span>
                </div>
                <p class="footer-tagline">Make your dream vacation affordable with our monthly prepaid EMI plans</p>
              </div>

              <div class="footer-links">
                <div class="footer-column">
                  <h4>Company</h4>
                  <a href="#about">About</a>
                  <a href="#careers">Careers</a>
                  <a href="#press">Press</a>
                </div>

                <div class="footer-column">
                  <h4>Support</h4>
                  <a href="#contact">Contact</a>
                  <a href="#help">Help/FAQ</a>
                  <a href="#support">Support</a>
                </div>

                <div class="footer-column">
                  <h4>Legal</h4>
                  <a href="#terms">Terms of Service</a>
                  <a href="#privacy">Privacy Policy</a>
                  <a href="#cancellation">Cancellation Policy</a>
                </div>
              </div>
            </div>

            <div class="footer-bottom">
              <div class="footer-address">
                <h5>Registered Office:</h5>
                <p>Najam Centre, 2nd Floor, 29/108, Old Bridge Road, Near Vishnu Cinemas, Viruthampet, Vellore, 632006</p>
              </div>
              <div class="footer-social">
                <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
                <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
              </div>
            </div>

            <div class="footer-copyright">
              <p>&copy; 2024 All Rights Reserved @TripXplo Tours Pvt Ltd</p>
            </div>
          </div>
        </footer>
      </div>
    </div>
  </body>
</html>
